{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 25108, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 25108, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 25108, "tid": 11913, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 25108, "tid": 11913, "ts": 1752405723327247, "dur": 989, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 25108, "tid": 11913, "ts": 1752405723331692, "dur": 984, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 25108, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 25108, "tid": 1, "ts": 1752405721797080, "dur": 6958, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 25108, "tid": 1, "ts": 1752405721804046, "dur": 71416, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 25108, "tid": 1, "ts": 1752405721875476, "dur": 446543, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 25108, "tid": 11913, "ts": 1752405723332682, "dur": 18, "ph": "X", "name": "", "args": {}}, {"pid": 25108, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721789236, "dur": 97, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721789334, "dur": 1529074, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721790174, "dur": 2997, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721793176, "dur": 1463, "ph": "X", "name": "ProcessMessages 20490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721794642, "dur": 812, "ph": "X", "name": "ReadAsync 20490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795458, "dur": 10, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795468, "dur": 202, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795673, "dur": 4, "ph": "X", "name": "ProcessMessages 8516", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795678, "dur": 38, "ph": "X", "name": "ReadAsync 8516", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795717, "dur": 1, "ph": "X", "name": "ProcessMessages 2412", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795719, "dur": 28, "ph": "X", "name": "ReadAsync 2412", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795750, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795751, "dur": 15, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795770, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795788, "dur": 14, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795804, "dur": 15, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795822, "dur": 19, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795844, "dur": 13, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795859, "dur": 17, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795879, "dur": 15, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795895, "dur": 15, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795913, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795933, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795951, "dur": 15, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795969, "dur": 17, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721795988, "dur": 14, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796005, "dur": 19, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796026, "dur": 14, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796042, "dur": 15, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796060, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796083, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796102, "dur": 16, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796120, "dur": 17, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796141, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796162, "dur": 13, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796177, "dur": 15, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796195, "dur": 32, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796229, "dur": 15, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796247, "dur": 15, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796264, "dur": 22, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796289, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796309, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796327, "dur": 24, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796354, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796372, "dur": 16, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796391, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796409, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796427, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796444, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796466, "dur": 14, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796483, "dur": 11, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796496, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796512, "dur": 117, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796631, "dur": 1, "ph": "X", "name": "ProcessMessages 2009", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796632, "dur": 39, "ph": "X", "name": "ReadAsync 2009", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796675, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796703, "dur": 25, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796731, "dur": 15, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796749, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796768, "dur": 14, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796785, "dur": 16, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796803, "dur": 14, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796819, "dur": 21, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796842, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796864, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796883, "dur": 20, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796907, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796909, "dur": 17, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796928, "dur": 16, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796946, "dur": 17, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796965, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721796982, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797001, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797020, "dur": 22, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797044, "dur": 19, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797066, "dur": 19, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797087, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797105, "dur": 15, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797123, "dur": 19, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797144, "dur": 606, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797752, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797753, "dur": 141, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797896, "dur": 4, "ph": "X", "name": "ProcessMessages 9201", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797901, "dur": 37, "ph": "X", "name": "ReadAsync 9201", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797939, "dur": 1, "ph": "X", "name": "ProcessMessages 1318", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797941, "dur": 17, "ph": "X", "name": "ReadAsync 1318", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797960, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797980, "dur": 16, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721797999, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798016, "dur": 16, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798035, "dur": 14, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798053, "dur": 19, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798076, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798098, "dur": 19, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798120, "dur": 18, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798141, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798164, "dur": 28, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798195, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798196, "dur": 54, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798253, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798255, "dur": 29, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798287, "dur": 17, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798308, "dur": 24, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798335, "dur": 54, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798394, "dur": 60, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798457, "dur": 61, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798520, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798521, "dur": 60, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798584, "dur": 63, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798648, "dur": 1, "ph": "X", "name": "ProcessMessages 1209", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798650, "dur": 56, "ph": "X", "name": "ReadAsync 1209", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798708, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798710, "dur": 30, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798742, "dur": 1, "ph": "X", "name": "ProcessMessages 1453", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798743, "dur": 19, "ph": "X", "name": "ReadAsync 1453", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798766, "dur": 14, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798784, "dur": 20, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798807, "dur": 16, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798826, "dur": 20, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798848, "dur": 19, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798871, "dur": 19, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798892, "dur": 16, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798912, "dur": 14, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798929, "dur": 16, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798947, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798973, "dur": 21, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721798996, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799016, "dur": 16, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799035, "dur": 32, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799070, "dur": 17, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799090, "dur": 30, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799124, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799156, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799157, "dur": 21, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799181, "dur": 20, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799207, "dur": 167, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799377, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799379, "dur": 49, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799431, "dur": 1, "ph": "X", "name": "ProcessMessages 2220", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799433, "dur": 28, "ph": "X", "name": "ReadAsync 2220", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799464, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799483, "dur": 26, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799514, "dur": 67, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799584, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799585, "dur": 30, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799619, "dur": 21, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799643, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799644, "dur": 24, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799672, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799674, "dur": 30, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799707, "dur": 65, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799776, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799779, "dur": 51, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799833, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799834, "dur": 59, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799896, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799898, "dur": 29, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799930, "dur": 54, "ph": "X", "name": "ReadAsync 1233", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721799990, "dur": 32, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800023, "dur": 1, "ph": "X", "name": "ProcessMessages 1611", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800025, "dur": 48, "ph": "X", "name": "ReadAsync 1611", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800078, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800109, "dur": 27, "ph": "X", "name": "ReadAsync 1239", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800140, "dur": 21, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800163, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800166, "dur": 16, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800184, "dur": 17, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800203, "dur": 3, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800207, "dur": 18, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800228, "dur": 18, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800249, "dur": 20, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800272, "dur": 18, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800294, "dur": 19, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800316, "dur": 18, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800337, "dur": 26, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800367, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800393, "dur": 19, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800415, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800437, "dur": 17, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800457, "dur": 13, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800473, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800495, "dur": 27, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800524, "dur": 17, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800544, "dur": 16, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800563, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800589, "dur": 15, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800606, "dur": 17, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800627, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800648, "dur": 19, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800670, "dur": 19, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800692, "dur": 17, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800712, "dur": 18, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800732, "dur": 26, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800761, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800763, "dur": 29, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800794, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800796, "dur": 38, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800836, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800838, "dur": 27, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800868, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800870, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800894, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800924, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800925, "dur": 33, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800962, "dur": 30, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721800997, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801033, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801035, "dur": 29, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801067, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801071, "dur": 30, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801104, "dur": 30, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801138, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801140, "dur": 33, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801178, "dur": 40, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801221, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801223, "dur": 28, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801253, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801255, "dur": 29, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801287, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801289, "dur": 29, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801321, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801324, "dur": 62, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801392, "dur": 30, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801425, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801428, "dur": 97, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801528, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801530, "dur": 44, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801578, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801580, "dur": 69, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801654, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801689, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801750, "dur": 73, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721801827, "dur": 619, "ph": "X", "name": "ProcessMessages 1994", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802448, "dur": 64, "ph": "X", "name": "ReadAsync 1994", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802514, "dur": 2, "ph": "X", "name": "ProcessMessages 3022", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802517, "dur": 22, "ph": "X", "name": "ReadAsync 3022", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802542, "dur": 20, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802565, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802585, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802610, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802630, "dur": 49, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802681, "dur": 22, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802705, "dur": 16, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802724, "dur": 14, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802740, "dur": 14, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802759, "dur": 21, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802782, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802804, "dur": 20, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802826, "dur": 14, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802843, "dur": 34, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802879, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802899, "dur": 16, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802918, "dur": 26, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802946, "dur": 15, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802964, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721802983, "dur": 19, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803004, "dur": 27, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803034, "dur": 17, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803053, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803075, "dur": 18, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803097, "dur": 17, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803116, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803139, "dur": 15, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803157, "dur": 85, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803244, "dur": 1, "ph": "X", "name": "ProcessMessages 1531", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803245, "dur": 23, "ph": "X", "name": "ReadAsync 1531", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803273, "dur": 23, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803299, "dur": 21, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803324, "dur": 19, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803346, "dur": 19, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803368, "dur": 28, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803399, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803437, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803439, "dur": 136, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803577, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803579, "dur": 53, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803635, "dur": 1, "ph": "X", "name": "ProcessMessages 2556", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803637, "dur": 22, "ph": "X", "name": "ReadAsync 2556", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803662, "dur": 25, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803691, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803717, "dur": 26, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803746, "dur": 22, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803771, "dur": 20, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803794, "dur": 17, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803815, "dur": 34, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803853, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803898, "dur": 25, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803926, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803927, "dur": 24, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803954, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803956, "dur": 24, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721803983, "dur": 15, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804000, "dur": 20, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804024, "dur": 24, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804050, "dur": 13, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804067, "dur": 19, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804088, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804110, "dur": 49, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804163, "dur": 19, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804184, "dur": 98, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804286, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804324, "dur": 1, "ph": "X", "name": "ProcessMessages 2361", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804325, "dur": 18, "ph": "X", "name": "ReadAsync 2361", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804346, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804369, "dur": 17, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804388, "dur": 18, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804409, "dur": 17, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804428, "dur": 18, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804450, "dur": 13, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804465, "dur": 17, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804485, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804516, "dur": 17, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804535, "dur": 17, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804554, "dur": 16, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804573, "dur": 18, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804593, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804615, "dur": 12, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804630, "dur": 35, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804667, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804688, "dur": 20, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804711, "dur": 17, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804731, "dur": 20, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804754, "dur": 18, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804775, "dur": 16, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804793, "dur": 18, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804813, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804835, "dur": 20, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804857, "dur": 17, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804877, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804898, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804922, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804939, "dur": 15, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804956, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804959, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721804983, "dur": 23, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805010, "dur": 24, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805036, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805038, "dur": 24, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805064, "dur": 24, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805091, "dur": 17, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805112, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805134, "dur": 22, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805159, "dur": 18, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805179, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805196, "dur": 17, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805217, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805234, "dur": 13, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805250, "dur": 14, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805266, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805284, "dur": 18, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805304, "dur": 19, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805326, "dur": 19, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805347, "dur": 23, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805374, "dur": 20, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805396, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805419, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805457, "dur": 17, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805476, "dur": 19, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805497, "dur": 17, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805517, "dur": 13, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805532, "dur": 24, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805559, "dur": 13, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805573, "dur": 12, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805588, "dur": 18, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805608, "dur": 19, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805630, "dur": 19, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805652, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805674, "dur": 17, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805694, "dur": 15, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805711, "dur": 16, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805730, "dur": 24, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805756, "dur": 20, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805779, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805800, "dur": 13, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805816, "dur": 18, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805836, "dur": 15, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805853, "dur": 15, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805870, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805900, "dur": 18, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805922, "dur": 26, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805951, "dur": 21, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805974, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721805997, "dur": 16, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806015, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806036, "dur": 23, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806062, "dur": 18, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806082, "dur": 16, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806100, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806121, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806140, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806161, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806181, "dur": 19, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806203, "dur": 19, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806224, "dur": 17, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806244, "dur": 18, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806264, "dur": 16, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806283, "dur": 13, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806298, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806317, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806319, "dur": 17, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806339, "dur": 23, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806364, "dur": 18, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806385, "dur": 32, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806419, "dur": 18, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806440, "dur": 14, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806457, "dur": 17, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806477, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806496, "dur": 17, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806515, "dur": 25, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806543, "dur": 18, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806564, "dur": 20, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806586, "dur": 13, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806602, "dur": 13, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806617, "dur": 19, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806639, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806660, "dur": 12, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806675, "dur": 28, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806705, "dur": 17, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806725, "dur": 22, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806749, "dur": 13, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806765, "dur": 20, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806787, "dur": 19, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806809, "dur": 19, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806831, "dur": 16, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806850, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806872, "dur": 20, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806895, "dur": 13, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806910, "dur": 19, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806932, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806952, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806970, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721806993, "dur": 17, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807012, "dur": 17, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807031, "dur": 13, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807047, "dur": 14, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807063, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807082, "dur": 27, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807111, "dur": 19, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807133, "dur": 13, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807148, "dur": 15, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807166, "dur": 25, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807193, "dur": 12, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807208, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807228, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807253, "dur": 15, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807270, "dur": 17, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807289, "dur": 19, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807310, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807335, "dur": 15, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807352, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807372, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807391, "dur": 18, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807411, "dur": 19, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807433, "dur": 17, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807452, "dur": 19, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807473, "dur": 17, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807494, "dur": 14, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807510, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807530, "dur": 16, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807548, "dur": 29, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807579, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807600, "dur": 15, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807618, "dur": 17, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807637, "dur": 18, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807658, "dur": 14, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807674, "dur": 13, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807690, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807709, "dur": 16, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807728, "dur": 17, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807747, "dur": 18, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807768, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807787, "dur": 19, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807808, "dur": 12, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807822, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807839, "dur": 13, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807854, "dur": 15, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807870, "dur": 13, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807886, "dur": 13, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807901, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807921, "dur": 18, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807942, "dur": 19, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807964, "dur": 15, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721807981, "dur": 15, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808000, "dur": 41, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808044, "dur": 19, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808065, "dur": 17, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808085, "dur": 17, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808104, "dur": 17, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808123, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808147, "dur": 23, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808172, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808247, "dur": 14, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808264, "dur": 21, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808287, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808309, "dur": 16, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808328, "dur": 17, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808347, "dur": 18, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808367, "dur": 13, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808383, "dur": 15, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808401, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808427, "dur": 16, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808446, "dur": 20, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808469, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808493, "dur": 17, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808512, "dur": 94, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808609, "dur": 1, "ph": "X", "name": "ProcessMessages 1659", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808611, "dur": 22, "ph": "X", "name": "ReadAsync 1659", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808636, "dur": 22, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808660, "dur": 24, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808687, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808690, "dur": 27, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808720, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808722, "dur": 31, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808757, "dur": 15, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808775, "dur": 16, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808793, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808814, "dur": 17, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808833, "dur": 29, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808863, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808865, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808884, "dur": 63, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808950, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808972, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721808989, "dur": 15, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809007, "dur": 17, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809026, "dur": 126, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809155, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809178, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809199, "dur": 20, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809222, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809242, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809266, "dur": 28, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809296, "dur": 15, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809313, "dur": 14, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809330, "dur": 78, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809410, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809429, "dur": 17, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809449, "dur": 18, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809470, "dur": 25, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809498, "dur": 23, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809523, "dur": 34, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809559, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809561, "dur": 18, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809584, "dur": 13, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809599, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809619, "dur": 71, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809692, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809715, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809735, "dur": 17, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809754, "dur": 13, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809770, "dur": 77, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809849, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809870, "dur": 25, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809898, "dur": 16, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809916, "dur": 80, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721809999, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810020, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810023, "dur": 32, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810057, "dur": 19, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810079, "dur": 21, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810103, "dur": 76, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810182, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810205, "dur": 25, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810233, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810250, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810252, "dur": 87, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810342, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810365, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810385, "dur": 29, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810417, "dur": 121, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810542, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810618, "dur": 1, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810620, "dur": 30, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810652, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810675, "dur": 21, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810699, "dur": 18, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810720, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810801, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810833, "dur": 19, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810855, "dur": 14, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810871, "dur": 76, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810950, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810972, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721810990, "dur": 20, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811014, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811031, "dur": 70, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811104, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811125, "dur": 15, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811142, "dur": 15, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811159, "dur": 14, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811175, "dur": 80, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811259, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811282, "dur": 20, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811305, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811323, "dur": 14, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811340, "dur": 71, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811414, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811435, "dur": 17, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811455, "dur": 25, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811482, "dur": 79, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811564, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811587, "dur": 27, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811617, "dur": 19, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811638, "dur": 78, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811719, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811743, "dur": 17, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811762, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811790, "dur": 16, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811809, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811885, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811905, "dur": 16, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811925, "dur": 16, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811944, "dur": 15, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721811961, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812040, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812063, "dur": 15, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812081, "dur": 17, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812101, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812117, "dur": 78, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812198, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812221, "dur": 23, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812246, "dur": 16, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812265, "dur": 86, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812353, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812375, "dur": 18, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812396, "dur": 19, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812418, "dur": 13, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812434, "dur": 72, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812508, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812530, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812554, "dur": 13, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812570, "dur": 15, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812588, "dur": 87, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812677, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812704, "dur": 18, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812725, "dur": 16, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812744, "dur": 71, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812817, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812838, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812856, "dur": 16, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812874, "dur": 16, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812893, "dur": 78, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812973, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721812994, "dur": 15, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813012, "dur": 18, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813032, "dur": 19, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813053, "dur": 77, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813132, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813156, "dur": 17, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813176, "dur": 17, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813196, "dur": 16, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813214, "dur": 83, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813299, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813322, "dur": 13, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813337, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813360, "dur": 13, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813376, "dur": 81, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813459, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813484, "dur": 18, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813505, "dur": 13, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813521, "dur": 74, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813598, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813624, "dur": 17, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813643, "dur": 16, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813661, "dur": 12, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813676, "dur": 75, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813754, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813773, "dur": 15, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813791, "dur": 17, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813811, "dur": 20, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813835, "dur": 28, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813867, "dur": 20, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813889, "dur": 19, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813910, "dur": 18, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813930, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813947, "dur": 19, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813969, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721813991, "dur": 74, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814067, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814108, "dur": 22, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814132, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814133, "dur": 68, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814203, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814230, "dur": 16, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814248, "dur": 15, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814265, "dur": 27, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814294, "dur": 73, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814369, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814388, "dur": 58, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814448, "dur": 1, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814449, "dur": 23, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814476, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814500, "dur": 24, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814526, "dur": 14, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814543, "dur": 15, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814560, "dur": 14, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814576, "dur": 71, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814649, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814673, "dur": 18, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814693, "dur": 13, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814709, "dur": 17, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814728, "dur": 73, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814804, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814825, "dur": 20, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814847, "dur": 17, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814866, "dur": 13, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814882, "dur": 76, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814960, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721814981, "dur": 18, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815001, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815024, "dur": 56, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815084, "dur": 38, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815124, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815158, "dur": 26, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815187, "dur": 20, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815210, "dur": 17, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815230, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815250, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815270, "dur": 14, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815287, "dur": 13, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815303, "dur": 15, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815321, "dur": 78, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815402, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815422, "dur": 15, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815440, "dur": 19, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815462, "dur": 16, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815481, "dur": 74, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815558, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815584, "dur": 20, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815607, "dur": 14, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815623, "dur": 78, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815704, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815729, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815731, "dur": 17, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815751, "dur": 26, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815779, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815801, "dur": 73, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815875, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815896, "dur": 19, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815918, "dur": 24, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721815945, "dur": 79, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816026, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816097, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816099, "dur": 51, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816153, "dur": 194, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816354, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816356, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816407, "dur": 1, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816409, "dur": 141, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816554, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816591, "dur": 29, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816624, "dur": 24, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816652, "dur": 112, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816768, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816798, "dur": 22, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816824, "dur": 22, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816850, "dur": 117, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721816971, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817057, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817059, "dur": 23, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817085, "dur": 111, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817200, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817229, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817252, "dur": 19, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817273, "dur": 4, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817278, "dur": 81, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817363, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817394, "dur": 22, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817419, "dur": 16, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817438, "dur": 19, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817460, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817482, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817558, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817585, "dur": 21, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817609, "dur": 15, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817627, "dur": 130, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817761, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817798, "dur": 1, "ph": "X", "name": "ProcessMessages 1185", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817800, "dur": 65, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817867, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817894, "dur": 27, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817924, "dur": 21, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721817948, "dur": 74, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818025, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818053, "dur": 24, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818080, "dur": 19, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818103, "dur": 74, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818180, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818237, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818262, "dur": 27, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818292, "dur": 13, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818307, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818386, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818409, "dur": 17, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818429, "dur": 20, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818453, "dur": 17, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818473, "dur": 73, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818550, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818575, "dur": 20, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818598, "dur": 17, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818618, "dur": 82, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818702, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818722, "dur": 21, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818746, "dur": 28, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818777, "dur": 73, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818854, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818880, "dur": 22, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818905, "dur": 15, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721818923, "dur": 78, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819003, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819042, "dur": 23, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819069, "dur": 37, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819108, "dur": 17, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819128, "dur": 75, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819205, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819227, "dur": 19, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819249, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819269, "dur": 78, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819349, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819367, "dur": 22, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819392, "dur": 21, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819416, "dur": 20, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819439, "dur": 76, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819518, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819546, "dur": 22, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819570, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819572, "dur": 17, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819591, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819661, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819683, "dur": 18, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819704, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819725, "dur": 13, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819740, "dur": 77, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819820, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819844, "dur": 17, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819864, "dur": 21, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819888, "dur": 79, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819970, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721819992, "dur": 17, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820011, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820033, "dur": 13, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820049, "dur": 73, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820124, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820147, "dur": 21, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820170, "dur": 39, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820214, "dur": 14, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820230, "dur": 82, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820314, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820338, "dur": 24, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820364, "dur": 78, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820445, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820512, "dur": 24, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820539, "dur": 102, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820645, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820683, "dur": 1, "ph": "X", "name": "ProcessMessages 1123", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820685, "dur": 57, "ph": "X", "name": "ReadAsync 1123", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820746, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820772, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820773, "dur": 24, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820800, "dur": 14, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820817, "dur": 76, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820897, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820923, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820945, "dur": 16, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721820963, "dur": 74, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821040, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821067, "dur": 16, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821086, "dur": 18, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821108, "dur": 105, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821216, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821240, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821242, "dur": 18, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821262, "dur": 18, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821284, "dur": 15, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821301, "dur": 76, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821380, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821409, "dur": 18, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821430, "dur": 20, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821455, "dur": 71, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821528, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821554, "dur": 16, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821574, "dur": 20, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821599, "dur": 77, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821684, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821715, "dur": 29, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821747, "dur": 17, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821768, "dur": 23, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821794, "dur": 16, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821813, "dur": 72, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821887, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821915, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821917, "dur": 22, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721821942, "dur": 76, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822022, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822049, "dur": 21, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822072, "dur": 76, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822150, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822175, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822177, "dur": 25, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822205, "dur": 68, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822277, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822301, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822303, "dur": 26, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822333, "dur": 21, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822357, "dur": 68, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822429, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822460, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822462, "dur": 23, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822487, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822489, "dur": 23, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822515, "dur": 32, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822550, "dur": 23, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822579, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822581, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822607, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822628, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822697, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822718, "dur": 15, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822736, "dur": 30, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822769, "dur": 17, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822789, "dur": 74, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822866, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822898, "dur": 53, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822954, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822956, "dur": 18, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721822977, "dur": 71, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823051, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823077, "dur": 17, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823096, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823115, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823133, "dur": 26, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823163, "dur": 41, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823207, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823208, "dur": 20, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823231, "dur": 23, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823258, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823280, "dur": 72, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823354, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823356, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823384, "dur": 27, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823414, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823415, "dur": 73, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823491, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823525, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823527, "dur": 27, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823557, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823558, "dur": 27, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823588, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823590, "dur": 27, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823620, "dur": 4, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823625, "dur": 27, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823656, "dur": 41, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823701, "dur": 21, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823725, "dur": 78, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823806, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823838, "dur": 23, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823865, "dur": 24, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823891, "dur": 85, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721823980, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824010, "dur": 25, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824039, "dur": 21, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824063, "dur": 19, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824089, "dur": 20, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824112, "dur": 21, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824136, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824138, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824159, "dur": 17, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824179, "dur": 71, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824254, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824279, "dur": 15, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824297, "dur": 17, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824317, "dur": 37, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824357, "dur": 18, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824377, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824399, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824422, "dur": 19, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824445, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824465, "dur": 17, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824485, "dur": 71, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824559, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824586, "dur": 23, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824613, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824641, "dur": 18, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824664, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824687, "dur": 14, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824705, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824727, "dur": 16, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824746, "dur": 16, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824765, "dur": 20, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824789, "dur": 67, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824858, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824881, "dur": 84, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824970, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721824972, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825001, "dur": 453, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825457, "dur": 61, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825521, "dur": 4, "ph": "X", "name": "ProcessMessages 960", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825527, "dur": 31, "ph": "X", "name": "ReadAsync 960", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825561, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825563, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825590, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825613, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825640, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825668, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825670, "dur": 22, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825699, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825725, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825726, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825761, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825763, "dur": 27, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825792, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825794, "dur": 26, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825823, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825825, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825851, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825875, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825879, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825907, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825909, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825937, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825939, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825964, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825966, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721825995, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826022, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826049, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826051, "dur": 21, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826075, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826077, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826107, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826110, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826141, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826143, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826172, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826174, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826202, "dur": 26, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826231, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826233, "dur": 25, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826261, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826263, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826290, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826292, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826320, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826322, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826348, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826350, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826376, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826403, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826405, "dur": 28, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826436, "dur": 26, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826465, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826467, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826498, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826499, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826529, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826533, "dur": 32, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826567, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826570, "dur": 30, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826603, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826605, "dur": 28, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826636, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826638, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826667, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826669, "dur": 30, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826701, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826703, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826733, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826735, "dur": 30, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826767, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826769, "dur": 25, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826796, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826798, "dur": 28, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826829, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826832, "dur": 26, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826862, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826864, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826893, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826895, "dur": 26, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826924, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826926, "dur": 28, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826958, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826960, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826990, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721826992, "dur": 29, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827024, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827026, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827061, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827064, "dur": 28, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827094, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827096, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827125, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827127, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827152, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827154, "dur": 19, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827175, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827177, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827206, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827235, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827237, "dur": 26, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827266, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827268, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827301, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827303, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827334, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827337, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827365, "dur": 80, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827449, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827480, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827482, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827511, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827512, "dur": 25, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827541, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827543, "dur": 31, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827577, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827580, "dur": 29, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827611, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827614, "dur": 32, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827648, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827650, "dur": 22, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827676, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827705, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827707, "dur": 24, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827734, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827735, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827767, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827769, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827790, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827792, "dur": 16, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827811, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827840, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827876, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827878, "dur": 23, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827905, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827926, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827928, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827957, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827991, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721827993, "dur": 21, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828017, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828019, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828050, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828052, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828083, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828085, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828118, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828120, "dur": 29, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828152, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828154, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828186, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828188, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828213, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828238, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828259, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828301, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828306, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721828327, "dur": 5384, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721833721, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721833725, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721833756, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721833759, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721833782, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721833784, "dur": 1150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721834941, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721834945, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721834980, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721835014, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721835036, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721835125, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721835160, "dur": 314, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721835479, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721835512, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721835515, "dur": 1203, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721836723, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721836726, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721836766, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721836769, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721836814, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721836819, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721836883, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721836886, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721836919, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721837099, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721837101, "dur": 778, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721837883, "dur": 547, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838435, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838459, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838502, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838559, "dur": 248, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838812, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838832, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838833, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838853, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838855, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838876, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838879, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838902, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721838923, "dur": 146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839073, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839094, "dur": 177, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839276, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839296, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839461, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839485, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839510, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839535, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839556, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839588, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839609, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839611, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839659, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839678, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839765, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839786, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839806, "dur": 118, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839929, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839945, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839961, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839963, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721839985, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840007, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840027, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840047, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840069, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840093, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840096, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840116, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840137, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840154, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840180, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840202, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840292, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840313, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840331, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840354, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840372, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840373, "dur": 102, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840478, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840480, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840499, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840519, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840545, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840561, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840563, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840583, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840602, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840640, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840659, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840675, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840698, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840713, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840730, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840746, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840762, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840781, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840796, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840821, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840841, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840860, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721840881, "dur": 165, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841050, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841079, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841101, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841179, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841205, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841224, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841331, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841355, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841379, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841396, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841414, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841416, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841533, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841556, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841571, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841612, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841636, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841639, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841879, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841904, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841905, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721841979, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842004, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842046, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842068, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842092, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842094, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842118, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842122, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842144, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842226, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842248, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842266, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842280, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842296, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842357, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842375, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842393, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842472, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842494, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842520, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842535, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842537, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842561, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842584, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842704, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842727, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842744, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842746, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842767, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842779, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842818, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842831, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842856, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842878, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842898, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842916, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842941, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842961, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721842987, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843057, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843080, "dur": 212, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843296, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843322, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843347, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843365, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843408, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843431, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843450, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843452, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843488, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843509, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843551, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843570, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843611, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843636, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843667, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843691, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843714, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843735, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843831, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843852, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843855, "dur": 37, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843899, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843924, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843948, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843972, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721843996, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844157, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844181, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844183, "dur": 54, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844241, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844337, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844339, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844362, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844365, "dur": 113, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844482, "dur": 433, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844919, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844954, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721844957, "dur": 225, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845187, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845210, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845233, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845253, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845254, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845281, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845295, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845323, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845325, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845347, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845370, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845398, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845420, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845470, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845498, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845527, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845582, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845607, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845635, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845659, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845682, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845706, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845725, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845812, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721845831, "dur": 669, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846503, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846553, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846557, "dur": 22, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846582, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846658, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846683, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846686, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846790, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846815, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846852, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846873, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846900, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846922, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846954, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721846974, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847060, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847079, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847102, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847104, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847164, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847186, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847320, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847348, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847350, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847375, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847517, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847545, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847546, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847576, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847607, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847611, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847641, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847669, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847707, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847708, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847833, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847860, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847862, "dur": 107, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847972, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847975, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721847997, "dur": 250, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721848251, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721848279, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721848281, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721848308, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721848344, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721848435, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721848437, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721848464, "dur": 602, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721849068, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721849070, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721849102, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721849104, "dur": 227, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721849336, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721849396, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721849414, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721849639, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721849666, "dur": 428, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721850098, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721850150, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721850153, "dur": 878, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721851036, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721851092, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405721851095, "dur": 450712, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722301824, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722301834, "dur": 289, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722302126, "dur": 1876, "ph": "X", "name": "ProcessMessages 4430", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722304006, "dur": 393232, "ph": "X", "name": "ReadAsync 4430", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722697246, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722697249, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722697312, "dur": 14, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722697329, "dur": 26211, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722723548, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722723552, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722723594, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722723597, "dur": 153475, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722877087, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722877092, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722877181, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722877186, "dur": 124, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722877316, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722877334, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722877336, "dur": 120040, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722997384, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722997387, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722997410, "dur": 17, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405722997428, "dur": 20794, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723018233, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723018237, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723018277, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723018279, "dur": 97852, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723116139, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723116142, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723116176, "dur": 17, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723116194, "dur": 19277, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723135480, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723135483, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723135552, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723135556, "dur": 1254, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723136816, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723136819, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723136930, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723136947, "dur": 37607, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723174564, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723174567, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723174587, "dur": 107208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723281809, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723281814, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723281840, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723281843, "dur": 1210, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723283063, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723283068, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723283109, "dur": 26, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723283137, "dur": 641, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723283784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723283786, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723283821, "dur": 524, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405723284348, "dur": 33989, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 25108, "tid": 11913, "ts": 1752405723332702, "dur": 2513, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 25108, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 25108, "tid": 8589934592, "ts": 1752405721786608, "dur": 535466, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 25108, "tid": 8589934592, "ts": 1752405722322077, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 25108, "tid": 8589934592, "ts": 1752405722322080, "dur": 1050, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 25108, "tid": 11913, "ts": 1752405723335222, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 25108, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 25108, "tid": 4294967296, "ts": 1752405721752964, "dur": 1566250, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752405721757302, "dur": 10547, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752405723319365, "dur": 4342, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752405723321704, "dur": 90, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752405723323831, "dur": 29, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 25108, "tid": 11913, "ts": 1752405723335228, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752405721782270, "dur": 2763, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405721785053, "dur": 2610, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405721787819, "dur": 130, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752405721787949, "dur": 573, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405721789350, "dur": 2594, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_D30C54CC03ED4A77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405721792741, "dur": 2339, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2804DA6C2AC419DA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405721795615, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_3AA533410EF50020.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405721795803, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405721796731, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Postprocessing.Runtime.ref.dll_591CFE2FB1206075.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405721797883, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752405721799446, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405721802589, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405721802737, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752405721808298, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752405721817117, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8786433236260407851.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405721818304, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Services.Core.Device.dll"}}, {"pid": 12345, "tid": 0, "ts": 1752405721788548, "dur": 36432, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405721824999, "dur": 1458250, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405723283250, "dur": 251, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405723283712, "dur": 127, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405723283880, "dur": 25256, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752405721788484, "dur": 36522, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721825166, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1D9B8A6D435426D1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721825258, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_A595DFC6B7DD6C4D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721825347, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721825625, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_0157079A87E77991.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721825706, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_982C2588BACDC823.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721826036, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721826280, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752405721826370, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752405721826668, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752405721826737, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752405721826811, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752405721826970, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752405721827108, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752405721827231, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752405721827461, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721827745, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752405721828153, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8824286945966794202.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752405721828268, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721828473, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721829121, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721829761, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721829956, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721830186, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721830631, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721830836, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721831060, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721831283, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721831480, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721831863, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Editor\\Lighting\\ProbeVolume\\ProbeGIBaking.LightTransport.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752405721831801, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721832634, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721832864, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721833044, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721833268, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\HandleMenuItem.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752405721833230, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721834215, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721835147, "dur": 1743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721836891, "dur": 1762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721838654, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721838975, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405721839660, "dur": 637, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721840316, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721840478, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721840774, "dur": 1389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405721842164, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721842235, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721842846, "dur": 1477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405721844406, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721844597, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405721845356, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721845597, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721845781, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405721846391, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721846481, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721846625, "dur": 1639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405721848266, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721848396, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721848542, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405721849088, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721849230, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405721849414, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405721849747, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405721850180, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405721851039, "dur": 70, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405721851819, "dur": 845452, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405722705180, "dur": 18323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752405722704836, "dur": 18750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405722724185, "dur": 150164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1752405722723628, "dur": 151752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405722876592, "dur": 395, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405722877743, "dur": 119737, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405723018138, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752405723018130, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752405723018277, "dur": 264970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721788584, "dur": 36439, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721825097, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CFC043736E130462.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405721825624, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721826339, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752405721826571, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752405721826868, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752405721827051, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405721827163, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405721827430, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405721827481, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721827677, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752405721827933, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16567978339051143262.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405721828288, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721828489, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721829206, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721829411, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721829619, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721829819, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721830014, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721830467, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721830673, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721830873, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721831085, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721831314, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721831527, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721831826, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721832027, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721832209, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721832393, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721832690, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721832902, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721833094, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721833621, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721834020, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721834993, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721835297, "dur": 1596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721836894, "dur": 1757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721838652, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405721838924, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405721839755, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721839894, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721840145, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405721840623, "dur": 1107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405721841731, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721842228, "dur": 604, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721842837, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405721843266, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721843409, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405721844347, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721844658, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405721844858, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721845022, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405721845726, "dur": 3507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721849233, "dur": 73497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721922731, "dur": 2671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405721925439, "dur": 2863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.WinRTStub.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405721928303, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721928437, "dur": 2425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405721930863, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721930968, "dur": 2457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405721933426, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721933526, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405721935970, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721936134, "dur": 2375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405721938510, "dur": 1724, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405721940241, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405721942673, "dur": 1340538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721788837, "dur": 36280, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721825123, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0EE7DB51FB8BE602.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405721825274, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_EADCC768BA19D973.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405721825400, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F664BA33EC4A029E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405721825623, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721826310, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Internal.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405721826452, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405721826740, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405721827061, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752405721827170, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752405721827459, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721827588, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752405721827716, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405721827899, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6535061076205788085.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405721828182, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11777448413204399724.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405721828288, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7033852615358263443.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405721828413, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721829071, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721829711, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721829886, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721830096, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721830577, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721830756, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721831552, "dur": 987, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\DynamicMatrixMaterialSlot.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752405721831003, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721832773, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721832956, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721833186, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721833574, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721833821, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721834048, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721834910, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721835128, "dur": 1777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721836905, "dur": 1775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721838681, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405721838890, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721838983, "dur": 1281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405721840264, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721840815, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405721841399, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721841473, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405721842202, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721842474, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405721842973, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405721843886, "dur": 810, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721844739, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405721844934, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721845028, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405721845754, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721845827, "dur": 3428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721849256, "dur": 73492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721922749, "dur": 2694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405721925444, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721925710, "dur": 5338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleStub.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405721931049, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721931470, "dur": 2832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405721934302, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721934540, "dur": 2965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405721937506, "dur": 723, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721938236, "dur": 2815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405721941089, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721941518, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721941804, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405721942001, "dur": 391591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405722333594, "dur": 949650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721788902, "dur": 36249, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721825245, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_9A8288CF5DD2C0CE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405721825307, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721825431, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_69785AF30F99F613.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405721825635, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721826126, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405721826338, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752405721826539, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752405721826642, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405721826791, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405721826905, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752405721827264, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752405721827479, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721827647, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752405721827842, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8542837875481535423.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405721828057, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1687125193959377702.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405721828300, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721828514, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721829139, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721829899, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721830117, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721830605, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721830823, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721831036, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721831276, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721831552, "dur": 768, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Runtime\\RenderPipelineResources\\UniversalRenderPipelineDebugShaders.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752405721832498, "dur": 920, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Runtime\\RendererFeatures\\FullScreenPassRendererFeature.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752405721831474, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721833432, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721833826, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics\\Unity.Mathematics\\float4x4.gen.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752405721833666, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721834892, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721835322, "dur": 1576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721836898, "dur": 1757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721838673, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405721839028, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721839704, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityStub.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405721840041, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityStub.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721840687, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721841203, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721841328, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721842023, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721842087, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405721842407, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405721842593, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721843058, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721843874, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721843948, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721844981, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721845055, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721845337, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721845393, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405721845633, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721846092, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721846197, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405721846371, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721847069, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405721847199, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721847709, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721847782, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405721847913, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721848391, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721849259, "dur": 73476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721922737, "dur": 4117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleCore.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721926855, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721927102, "dur": 2587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721929690, "dur": 1751, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721931446, "dur": 2622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721934069, "dur": 2735, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721936812, "dur": 3000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721939813, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405721939878, "dur": 2771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405721942724, "dur": 1340489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721788607, "dur": 36425, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721825096, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_87D80D01DE8F4B44.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405721825374, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_9F5344B509F71D5B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405721825463, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_EACEF57EE50C357D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405721825634, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D18D531254CCDD3B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405721826456, "dur": 243, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752405721826904, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405721827201, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1752405721827421, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721827620, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405721827702, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/IAPResolver.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405721827889, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6987655401940753962.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405721828221, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13069972837483359549.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405721828284, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721828543, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721829273, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721829475, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721829736, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721829940, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721830191, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721830638, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721830832, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721831048, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721831277, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721831479, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721831785, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721832006, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721832305, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721832911, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721833101, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721833736, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721833872, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721834189, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721835317, "dur": 1586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721836904, "dur": 1755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721838660, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405721838930, "dur": 704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721839635, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721839778, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTStub.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405721840166, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTStub.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721840791, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721840901, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721841487, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721841649, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721842261, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721842366, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405721842587, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721843368, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721843524, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721843603, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721844411, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405721844643, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721845224, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721845519, "dur": 3710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721849230, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405721849453, "dur": 73286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721922740, "dur": 2702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721925491, "dur": 2728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.SecurityStub.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721928258, "dur": 2602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721930861, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721931255, "dur": 2611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721933867, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721934294, "dur": 3384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721937684, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721937842, "dur": 2834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405721940677, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721941011, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721941694, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721941872, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405721942616, "dur": 1340623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721788667, "dur": 36375, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721825048, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5160C8205B154597.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721825173, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721825270, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_655979F918F27A39.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721825491, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721825609, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_A89A756DEE006762.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721825678, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_D37EEA1667D204B7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721826343, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1752405721826679, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1752405721826775, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTStub.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405721826902, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405721827208, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1752405721827307, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405721827468, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721827569, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405721827713, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1752405721827961, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11148342699003047676.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405721828218, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13245841461569978743.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405721828338, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721828622, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721829252, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721829463, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721829702, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721829893, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721830100, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721830566, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721830973, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721831652, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721831841, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721832026, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721832201, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721832380, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721832653, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721832852, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721833039, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721833314, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\DrawSearchField.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752405721833313, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721834079, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721834868, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721835119, "dur": 1789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721836908, "dur": 1769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721838679, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721838924, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721839537, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721839647, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721839891, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721840105, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721840409, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721840610, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Analytics.DataPrivacy.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721840832, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Analytics.DataPrivacy.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721841403, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721841465, "dur": 1321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721842786, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721843013, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721843256, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721843411, "dur": 867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721844350, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721844713, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721845299, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721845364, "dur": 1428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721846793, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405721846972, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721847516, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721847715, "dur": 1551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721849267, "dur": 73478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721922746, "dur": 2990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721925736, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721925984, "dur": 2509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Scheduler.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721928526, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Telemetry.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721931209, "dur": 2569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.Codeless.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721933779, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721934205, "dur": 3858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721938065, "dur": 706, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721938777, "dur": 2720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405721941605, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721941871, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405721941960, "dur": 381227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405722324754, "dur": 6685, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1752405722331442, "dur": 1944, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1752405722333389, "dur": 184, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1752405722323188, "dur": 10396, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405722333586, "dur": 949639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721788689, "dur": 36360, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721825057, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_4CE2C9A0BAA62A6D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405721825614, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721825774, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_FBDCCF39A60FD123.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405721825862, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_FBDCCF39A60FD123.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405721826223, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405721826387, "dur": 7336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405721833810, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721833902, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721834222, "dur": 1012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721835304, "dur": 1592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721836897, "dur": 1771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721838669, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405721838911, "dur": 657, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721839573, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405721840480, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405721840668, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721840786, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405721841000, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721841292, "dur": 1107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405721842400, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721842591, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405721842983, "dur": 705, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405721843689, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721843790, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721843851, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721844381, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/IAPResolver.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405721844704, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/IAPResolver.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405721845350, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721845486, "dur": 1488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721846975, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405721847168, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405721847707, "dur": 1527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721849235, "dur": 73493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721922729, "dur": 2972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.SecurityCore.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405721925702, "dur": 2070, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721927778, "dur": 2694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405721930473, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721930565, "dur": 2574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Components.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405721933139, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721933524, "dur": 3206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405721936766, "dur": 2944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405721939711, "dur": 783, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721940531, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721940667, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721940903, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721941090, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721941190, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721941688, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721941870, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405721942538, "dur": 1192742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405723135301, "dur": 239, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752405723135281, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752405723135565, "dur": 1376, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752405723136947, "dur": 146291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721788708, "dur": 36350, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721825065, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_0AA979E9F85FAA0B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405721825366, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_061CFA2463039477.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405721825596, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_E88B09B6A6B366F7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405721825654, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_2EB828704772EA6F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405721826305, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405721826426, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752405721826811, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752405721827029, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752405721827260, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.XR.LegacyInputHelpers.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752405721827359, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405721827494, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721827588, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405721827740, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/IAPResolver.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752405721827841, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3755261912168333632.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405721828014, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3755261912168333632.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405721828082, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6855193988388283930.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405721828153, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6855193988388283930.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405721828282, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721828541, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721829271, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721829530, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721829735, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721829911, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721830118, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721830567, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721830751, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721830988, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721831967, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721832444, "dur": 835, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\PostProcessing\\LensFlareCommonSRP.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752405721832309, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721833354, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721834118, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721835312, "dur": 1599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721836912, "dur": 1758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721838676, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405721838950, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721839032, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721839936, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721840078, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405721840411, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721840915, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721841504, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721842032, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721842201, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405721842676, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405721842863, "dur": 872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721843736, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721844063, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Advertisements.ref.dll_455FD91917F5E8E4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405721844397, "dur": 633, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721845053, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1752405721845311, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721845471, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721845773, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405721845949, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721846326, "dur": 2914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721849240, "dur": 73486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721922728, "dur": 2727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721925456, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721925529, "dur": 2609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721928142, "dur": 788, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721928937, "dur": 2697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Device.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721931667, "dur": 2860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721934561, "dur": 2555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Analytics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721937117, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721937277, "dur": 2766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721940044, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405721940394, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405721942773, "dur": 1340485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721788729, "dur": 36337, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721825072, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D7C1E683AFA161F2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405721825352, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721825620, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_0E396E09F947CCB7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405721825699, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_BF2E152E6652ABDE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405721826362, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405721826545, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1752405721826670, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405721826810, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752405721827164, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752405721827476, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721827590, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752405721827717, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1752405721827876, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17839364298002531413.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405721827957, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4763750419435726923.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405721828058, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17392071123951179896.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405721828284, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721828475, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721829129, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721829709, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721829892, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721830114, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721830596, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721830782, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721830992, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721831849, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721832127, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721832318, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721832604, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721832814, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721833009, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721833193, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721833427, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721833626, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721833969, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721834797, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721835102, "dur": 1779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721836928, "dur": 1733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721838663, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405721838969, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405721839702, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721839780, "dur": 462, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721840293, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405721840723, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721840943, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405721841523, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721841998, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405721842856, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721842957, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721843172, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405721843438, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721843616, "dur": 1147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405721844764, "dur": 723, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721845519, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721845578, "dur": 3648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721849228, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405721849436, "dur": 73308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721922745, "dur": 2795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Threading.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405721925541, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721925703, "dur": 2999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405721928703, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721929093, "dur": 2639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405721931733, "dur": 2369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721934108, "dur": 2710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405721936819, "dur": 1150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721937976, "dur": 2851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.Purchasing.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405721940828, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721941352, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721941488, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721941874, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405721942682, "dur": 1340563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721788773, "dur": 36393, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721825168, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_38C2F330433306EF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721825268, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_549D4A511CBCEDDD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721825405, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721825645, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBA2C8071FCB7521.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721825777, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_9FE533F5CA511E7D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721826366, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752405721826546, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1752405721826703, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405721826804, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752405721826965, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405721827095, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405721827264, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1752405721827391, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405721827480, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721827570, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405721827718, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1752405721827902, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13415310291297307087.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405721828013, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13415310291297307087.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405721828126, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6832337476128879279.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405721828318, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721828548, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721829279, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721829505, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721829716, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721829921, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721830126, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721830583, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721830928, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721831158, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721831384, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721831606, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721831803, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721832001, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721832200, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721832495, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Debugging\\DebugDisplaySettingsStats.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752405721832393, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721833406, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721833691, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721833962, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721834981, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721835135, "dur": 1775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721836910, "dur": 1764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721838683, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721838962, "dur": 821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721839784, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721840151, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721840211, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721840595, "dur": 1112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721841708, "dur": 1104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721842820, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721843043, "dur": 888, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721843943, "dur": 995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721844986, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721845043, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721845258, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721845379, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721846039, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721846216, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721846905, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721846971, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721847135, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721847273, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721847947, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721848084, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721848449, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721849248, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405721849479, "dur": 73252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721922734, "dur": 2358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721925139, "dur": 4335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721929475, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721929641, "dur": 2523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721932166, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721932356, "dur": 2834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Windsurf.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721935191, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721935528, "dur": 2624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721938153, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721938220, "dur": 2397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405721940618, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721940856, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721940965, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721941191, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721941620, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721941876, "dur": 775, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405721942688, "dur": 1340520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721788749, "dur": 36327, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721825085, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_12BB074DEDA731F9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405721825670, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721826329, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1752405721826430, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1752405721826546, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405721826739, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405721826999, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405721827260, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1752405721827403, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405721827461, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721827582, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Analytics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1752405721827887, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9138376835669937927.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405721828119, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721828277, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721828480, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721829158, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721829355, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721829561, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721829761, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721829967, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721830502, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721830709, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721830956, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721831224, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721831443, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721832043, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721832234, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721832721, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721833071, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721833908, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721834171, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721835319, "dur": 1581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721836901, "dur": 2051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721838953, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405721839192, "dur": 1948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405721841141, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721841196, "dur": 1567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405721842764, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721842884, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Networking.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405721843095, "dur": 1301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Networking.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405721844397, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721844737, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721844910, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721845515, "dur": 3752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721849267, "dur": 73476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721922744, "dur": 2649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405721925394, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721925469, "dur": 2742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405721928212, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721928355, "dur": 7221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Purchasing.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405721935577, "dur": 778, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721936363, "dur": 2338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405721938746, "dur": 2610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405721941511, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721941716, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721941802, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721941930, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405721942789, "dur": 1340467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721788797, "dur": 36303, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721825106, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_76BE4E04C75E42FB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405721825176, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721825241, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0FEF1F0834F797BE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405721825638, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_8C8256DD42673B8D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405721826298, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405721826489, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1752405721826738, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405721827027, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405721827267, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1752405721827457, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721827563, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405721827718, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405721827884, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16668691694592126932.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405721827970, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16584851037979262786.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405721828253, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16059909043995952555.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405721828310, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721828546, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721829229, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721829428, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721829675, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721829879, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721830072, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721830546, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721830886, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721831171, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721831383, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721831585, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721831782, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721832498, "dur": 871, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Debugging\\ProfilingScope.cs"}}, {"pid": 12345, "tid": 12, "ts": 1752405721832340, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721833479, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721833833, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721834726, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.34f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752405721834087, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721835461, "dur": 1424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721836886, "dur": 1804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721838692, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405721838949, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405721839006, "dur": 951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721839957, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721840166, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405721840472, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405721840696, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405721840889, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721840985, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721841777, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721841995, "dur": 917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721842912, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721843027, "dur": 802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721843830, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721844083, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721844280, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721844408, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405721845075, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721845325, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721846037, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405721846170, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721846789, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405721846966, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721847631, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721847740, "dur": 1507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721849251, "dur": 73499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721922751, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721925281, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721925846, "dur": 2824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721928671, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721928800, "dur": 2557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721931358, "dur": 1168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721932532, "dur": 2465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721935035, "dur": 4420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721939457, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721939605, "dur": 2819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/IAPResolver.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405721942426, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405721942525, "dur": 1075638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405723018185, "dur": 156418, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752405723018165, "dur": 156440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752405723174641, "dur": 108569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721788817, "dur": 36292, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721825116, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A430C75F08F988D8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721825173, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721825395, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_41B99F78B7E58F04.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721825617, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_B6BDE914FF775D37.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721825693, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C2741807B1FC8886.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721825844, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C2741807B1FC8886.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721826181, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721826307, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721826372, "dur": 8032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721834406, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721835108, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721835297, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721835497, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721835579, "dur": 1184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721836901, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721836979, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721837205, "dur": 1303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721838660, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721838926, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721839663, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721840271, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721840666, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721840853, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721840978, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721841708, "dur": 449, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721842223, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405721842565, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721842683, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721843381, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721843489, "dur": 1746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1752405721845266, "dur": 316, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721845940, "dur": 67843, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 13, "ts": 1752405721922736, "dur": 2494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721925231, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721925422, "dur": 2702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Analytics.DataPrivacy.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721928126, "dur": 622, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721928755, "dur": 2224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721930979, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721931356, "dur": 2588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.WinRTCore.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721933945, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405721934317, "dur": 2643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Configuration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721937013, "dur": 2836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721939892, "dur": 2640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405721942603, "dur": 1340639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721788528, "dur": 36488, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721825100, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_74CBFBDE1FABEA1C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405721825459, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_E35C72EE794F776F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405721825607, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FCFF39C391BCB29A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405721825661, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_6C968D1B8DB525D3.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405721825798, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721826423, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405721826571, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1752405721826905, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1752405721827166, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1752405721827473, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721828155, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13821562750565263257.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405721828275, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721828494, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721829139, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721829372, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721829563, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721829773, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721830038, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721830501, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721830729, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721830952, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721831141, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721831361, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721831584, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721831776, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721831969, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721832186, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721832379, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721832677, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721832890, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721833085, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721833511, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721833703, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721834230, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721834763, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721835003, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721835116, "dur": 1764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721836934, "dur": 1730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721838666, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405721838965, "dur": 604, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405721839571, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721840364, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721840469, "dur": 467, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721840977, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721841616, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721841681, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721842407, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721842505, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405721843097, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721843186, "dur": 1061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721844320, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405721844611, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405721844906, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721845470, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721846026, "dur": 3219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721849246, "dur": 73487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721922745, "dur": 2623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Registration.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721925369, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721925533, "dur": 2764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721928349, "dur": 2742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Networking.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721931092, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721931596, "dur": 2512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721934139, "dur": 3865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721938041, "dur": 2822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405721940864, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721941710, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721941886, "dur": 818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405721942729, "dur": 1340519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721788853, "dur": 36272, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721825133, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WebGLModule.dll_7671FA4B6C66E14A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721825192, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721825341, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721825709, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_153685F339F97A2F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721825843, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_153685F339F97A2F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721826706, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Internal.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405721826868, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405721826993, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405721827100, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405721827266, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1752405721827446, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721827719, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1752405721828251, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3948950754348299336.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405721828362, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721828673, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721829353, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721829566, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721829786, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721829960, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721830593, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721830770, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721831003, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721831436, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721831961, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Editor\\Settings\\PropertyDrawers\\RenderPipelineGraphicsSettingsContainerPropertyDrawer.cs"}}, {"pid": 12345, "tid": 15, "ts": 1752405721831756, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721832696, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721832929, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721833157, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721833474, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721833666, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721834001, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721835022, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721835112, "dur": 1779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721836892, "dur": 2080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721838974, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721839168, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721839393, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721839632, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721839917, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721840208, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721840433, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721840635, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721840710, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721840957, "dur": 1183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405721842140, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721842405, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721842656, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405721843344, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721843568, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721843668, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721843957, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405721844642, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405721845318, "dur": 1195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721846515, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405721846658, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405721847170, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721847482, "dur": 1786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721849268, "dur": 76012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721925282, "dur": 2624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.Stores.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405721927907, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721927963, "dur": 2618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Configuration.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405721930582, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721930726, "dur": 2527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405721933257, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721933314, "dur": 2618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.DevX.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405721935932, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721936125, "dur": 3970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405721940096, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405721940154, "dur": 2439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405721942671, "dur": 1340556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721788874, "dur": 36264, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721825145, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_597300EC37484E29.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405721825247, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A4F012C5A38D9152.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405721825385, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_E2923F1ADB85ED4A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405721825611, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_41A5F8FAB82719AF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405721825683, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_C7CE81B115F5925C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405721826327, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1752405721826431, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405721826903, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1752405721827112, "dur": 194, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405721827374, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405721827494, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721827739, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1752405721827869, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4769955788402727329.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405721827992, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17493449851061911080.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405721828185, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5814885297447240720.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405721828303, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721828512, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721829169, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721829359, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721829554, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721829797, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721830006, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721830484, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721830695, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721830890, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721831128, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721831347, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721831911, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721832129, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721832496, "dur": 872, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\CommandBuffers\\IBaseCommandBuffer.cs"}}, {"pid": 12345, "tid": 16, "ts": 1752405721832496, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721833547, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721833756, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721833849, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721834042, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721834993, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721835099, "dur": 1785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721836885, "dur": 1772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721838658, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405721838939, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405721839172, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721839584, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721840320, "dur": 847, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721841167, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721841479, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405721841722, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721842552, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721842636, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405721842849, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721843520, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721843746, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721843847, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721843977, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721844186, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721844348, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405721844496, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721844692, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721845300, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721845442, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405721845695, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721846190, "dur": 3070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721849261, "dur": 73480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721922742, "dur": 2937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721925680, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721925761, "dur": 2838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721928603, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721928758, "dur": 2653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721931465, "dur": 2686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleMacosStub.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721934152, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721934838, "dur": 2863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721937703, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721937838, "dur": 2739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405721940578, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721940981, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721941444, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721941791, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405721942242, "dur": 762625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405722704890, "dur": 169459, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752405722704870, "dur": 171017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405722877127, "dur": 281, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405722877943, "dur": 238264, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405723135254, "dur": 146573, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752405723135247, "dur": 146582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752405723281865, "dur": 1293, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1752405723316159, "dur": 1862, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 25108, "tid": 11913, "ts": 1752405723335744, "dur": 11523, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 25108, "tid": 11913, "ts": 1752405723347299, "dur": 2146, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 25108, "tid": 11913, "ts": 1752405723330401, "dur": 19850, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}