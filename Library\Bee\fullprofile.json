{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 25108, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 25108, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 25108, "tid": 11727, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 25108, "tid": 11727, "ts": 1752403594256130, "dur": 892, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 25108, "tid": 11727, "ts": 1752403594261300, "dur": 844, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 25108, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 25108, "tid": 1, "ts": 1752403592933434, "dur": 6830, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 25108, "tid": 1, "ts": 1752403592940271, "dur": 78655, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 25108, "tid": 1, "ts": 1752403593018948, "dur": 79479, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 25108, "tid": 11727, "ts": 1752403594262149, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 25108, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592931209, "dur": 5263, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592936479, "dur": 1310502, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592937632, "dur": 2453, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592940093, "dur": 1194, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592941291, "dur": 375, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592941669, "dur": 392, "ph": "X", "name": "ProcessMessages 20494", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942063, "dur": 167, "ph": "X", "name": "ReadAsync 20494", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942234, "dur": 6, "ph": "X", "name": "ProcessMessages 13020", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942241, "dur": 20, "ph": "X", "name": "ReadAsync 13020", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942264, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942290, "dur": 13, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942306, "dur": 26, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942340, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942359, "dur": 19, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942381, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942406, "dur": 17, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942426, "dur": 16, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942446, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942471, "dur": 53, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942527, "dur": 28, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942557, "dur": 1, "ph": "X", "name": "ProcessMessages 1144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942558, "dur": 35, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942595, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942612, "dur": 17, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942632, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942653, "dur": 19, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942674, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942693, "dur": 15, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942711, "dur": 30, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942744, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942767, "dur": 23, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942792, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942874, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942876, "dur": 51, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942929, "dur": 1, "ph": "X", "name": "ProcessMessages 1514", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942931, "dur": 45, "ph": "X", "name": "ReadAsync 1514", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592942979, "dur": 22, "ph": "X", "name": "ReadAsync 1091", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943003, "dur": 18, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943024, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943025, "dur": 72, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943100, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943123, "dur": 25, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943150, "dur": 23, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943176, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943178, "dur": 31, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943212, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943233, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943235, "dur": 15, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943252, "dur": 23, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943277, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943311, "dur": 21, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943334, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943337, "dur": 20, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943360, "dur": 23, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943385, "dur": 19, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943407, "dur": 20, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943431, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943464, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943465, "dur": 24, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943492, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943493, "dur": 22, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943519, "dur": 19, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943541, "dur": 18, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943562, "dur": 18, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943583, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943604, "dur": 18, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943625, "dur": 21, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943648, "dur": 16, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943669, "dur": 19, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943691, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943708, "dur": 13, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943724, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943741, "dur": 18, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943762, "dur": 16, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943781, "dur": 17, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943801, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943825, "dur": 54, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943882, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943907, "dur": 20, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592943930, "dur": 364, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944297, "dur": 150, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944450, "dur": 4, "ph": "X", "name": "ProcessMessages 7093", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944455, "dur": 43, "ph": "X", "name": "ReadAsync 7093", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944500, "dur": 1, "ph": "X", "name": "ProcessMessages 1352", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944502, "dur": 55, "ph": "X", "name": "ReadAsync 1352", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944563, "dur": 35, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944601, "dur": 2, "ph": "X", "name": "ProcessMessages 1266", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944604, "dur": 23, "ph": "X", "name": "ReadAsync 1266", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944630, "dur": 23, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944656, "dur": 18, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944677, "dur": 19, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944699, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944803, "dur": 41, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944846, "dur": 1, "ph": "X", "name": "ProcessMessages 1663", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944849, "dur": 30, "ph": "X", "name": "ReadAsync 1663", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944882, "dur": 18, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944902, "dur": 25, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944930, "dur": 32, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944966, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592944968, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945000, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945002, "dur": 61, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945067, "dur": 40, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945110, "dur": 1, "ph": "X", "name": "ProcessMessages 1159", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945113, "dur": 90, "ph": "X", "name": "ReadAsync 1159", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945205, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945208, "dur": 27, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945238, "dur": 30, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945271, "dur": 20, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945294, "dur": 57, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945355, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945397, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945400, "dur": 32, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945435, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945437, "dur": 33, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945474, "dur": 100, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945576, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945578, "dur": 23, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945607, "dur": 22, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945631, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945633, "dur": 14, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945650, "dur": 31, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945686, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945724, "dur": 1, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945725, "dur": 22, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945750, "dur": 20, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945772, "dur": 44, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945819, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945821, "dur": 60, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945884, "dur": 74, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945959, "dur": 1, "ph": "X", "name": "ProcessMessages 1491", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592945960, "dur": 63, "ph": "X", "name": "ReadAsync 1491", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946027, "dur": 60, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946089, "dur": 1, "ph": "X", "name": "ProcessMessages 1246", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946090, "dur": 25, "ph": "X", "name": "ReadAsync 1246", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946119, "dur": 24, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946145, "dur": 18, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946166, "dur": 64, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946233, "dur": 65, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946301, "dur": 68, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946371, "dur": 1, "ph": "X", "name": "ProcessMessages 1418", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946372, "dur": 60, "ph": "X", "name": "ReadAsync 1418", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946436, "dur": 63, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946500, "dur": 1, "ph": "X", "name": "ProcessMessages 1296", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946502, "dur": 64, "ph": "X", "name": "ReadAsync 1296", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946569, "dur": 34, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946605, "dur": 1, "ph": "X", "name": "ProcessMessages 1483", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946608, "dur": 17, "ph": "X", "name": "ReadAsync 1483", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946627, "dur": 17, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946647, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946670, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946691, "dur": 28, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946721, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946744, "dur": 21, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946768, "dur": 14, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946783, "dur": 16, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946802, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946825, "dur": 16, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946844, "dur": 21, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946868, "dur": 19, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946889, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946909, "dur": 16, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946928, "dur": 19, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946951, "dur": 25, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592946981, "dur": 34, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947017, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947018, "dur": 27, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947048, "dur": 18, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947069, "dur": 17, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947088, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947108, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947131, "dur": 14, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947148, "dur": 120, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947269, "dur": 1, "ph": "X", "name": "ProcessMessages 1489", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947271, "dur": 25, "ph": "X", "name": "ReadAsync 1489", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947298, "dur": 22, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947323, "dur": 23, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947348, "dur": 18, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947369, "dur": 16, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947388, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947405, "dur": 20, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947427, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947458, "dur": 13, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947473, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947494, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947516, "dur": 18, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947536, "dur": 18, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947558, "dur": 22, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947583, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947603, "dur": 17, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947622, "dur": 30, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947655, "dur": 41, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947698, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947700, "dur": 24, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947726, "dur": 17, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947747, "dur": 20, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947771, "dur": 22, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947796, "dur": 24, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947823, "dur": 25, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947851, "dur": 21, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947876, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947903, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947929, "dur": 42, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947973, "dur": 17, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592947993, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948014, "dur": 20, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948037, "dur": 25, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948066, "dur": 62, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948132, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948169, "dur": 1, "ph": "X", "name": "ProcessMessages 1979", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948171, "dur": 17, "ph": "X", "name": "ReadAsync 1979", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948191, "dur": 36, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948230, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948249, "dur": 16, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948268, "dur": 19, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948290, "dur": 19, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948312, "dur": 17, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948332, "dur": 17, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948351, "dur": 2, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948353, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948371, "dur": 19, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948393, "dur": 13, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948409, "dur": 18, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948429, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948449, "dur": 16, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948468, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948491, "dur": 17, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948512, "dur": 17, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948532, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948553, "dur": 59, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948616, "dur": 38, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948657, "dur": 1, "ph": "X", "name": "ProcessMessages 1410", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948658, "dur": 27, "ph": "X", "name": "ReadAsync 1410", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948688, "dur": 18, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948708, "dur": 56, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948768, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948811, "dur": 1, "ph": "X", "name": "ProcessMessages 1798", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948812, "dur": 22, "ph": "X", "name": "ReadAsync 1798", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948838, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948856, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948880, "dur": 20, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948903, "dur": 19, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948925, "dur": 14, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948941, "dur": 24, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948968, "dur": 18, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592948990, "dur": 12, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949005, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949028, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949049, "dur": 17, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949068, "dur": 16, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949086, "dur": 23, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949111, "dur": 17, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949133, "dur": 17, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949152, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949169, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949195, "dur": 17, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949214, "dur": 21, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949238, "dur": 17, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949257, "dur": 16, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949276, "dur": 15, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949294, "dur": 17, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949313, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949335, "dur": 37, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949374, "dur": 18, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949396, "dur": 16, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949415, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949435, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949471, "dur": 24, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949497, "dur": 221, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949719, "dur": 48, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949770, "dur": 2, "ph": "X", "name": "ProcessMessages 3048", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949773, "dur": 24, "ph": "X", "name": "ReadAsync 3048", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949799, "dur": 20, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949823, "dur": 16, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949841, "dur": 19, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949863, "dur": 22, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949890, "dur": 26, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949918, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949942, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949965, "dur": 23, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592949990, "dur": 19, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950012, "dur": 23, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950037, "dur": 18, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950058, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950079, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950100, "dur": 90, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950193, "dur": 32, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950228, "dur": 19, "ph": "X", "name": "ReadAsync 1286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950251, "dur": 27, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950280, "dur": 22, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950305, "dur": 18, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950326, "dur": 18, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950346, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950365, "dur": 15, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950382, "dur": 20, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950405, "dur": 15, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950422, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950444, "dur": 17, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950463, "dur": 24, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950489, "dur": 18, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950510, "dur": 17, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950530, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950552, "dur": 18, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950573, "dur": 20, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950596, "dur": 24, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950622, "dur": 18, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950642, "dur": 14, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950659, "dur": 15, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950676, "dur": 24, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950702, "dur": 13, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950718, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950738, "dur": 17, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950758, "dur": 17, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950777, "dur": 17, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950797, "dur": 14, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950812, "dur": 15, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950830, "dur": 15, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950848, "dur": 18, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950868, "dur": 18, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950888, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950913, "dur": 17, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950933, "dur": 19, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950955, "dur": 13, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950970, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592950986, "dur": 14, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951003, "dur": 20, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951027, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951048, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951073, "dur": 29, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951106, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951108, "dur": 29, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951140, "dur": 34, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951178, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951211, "dur": 25, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951239, "dur": 21, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951262, "dur": 25, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951290, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951292, "dur": 30, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951325, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951327, "dur": 35, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951366, "dur": 22, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951392, "dur": 38, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951433, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951435, "dur": 28, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951465, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951467, "dur": 34, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951505, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951524, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951549, "dur": 23, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951575, "dur": 22, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951599, "dur": 17, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951619, "dur": 21, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951643, "dur": 18, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951664, "dur": 23, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951688, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951743, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951765, "dur": 21, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951788, "dur": 29, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951822, "dur": 25, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951855, "dur": 24, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951883, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951906, "dur": 70, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592951981, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952022, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952023, "dur": 86, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952112, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952114, "dur": 72, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952189, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952191, "dur": 20, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952214, "dur": 84, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952301, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952332, "dur": 24, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952358, "dur": 22, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952383, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952405, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952425, "dur": 33, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952461, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952499, "dur": 25, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952526, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952528, "dur": 44, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952574, "dur": 18, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952595, "dur": 19, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952617, "dur": 17, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952637, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952657, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952677, "dur": 17, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952697, "dur": 30, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952731, "dur": 18, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952751, "dur": 24, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952777, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952781, "dur": 16, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952800, "dur": 16, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952819, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952856, "dur": 23, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952882, "dur": 16, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952900, "dur": 19, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952922, "dur": 17, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952942, "dur": 26, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592952972, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953002, "dur": 99, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953104, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953105, "dur": 25, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953133, "dur": 20, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953159, "dur": 19, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953181, "dur": 37, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953223, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953263, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953265, "dur": 22, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953290, "dur": 20, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953314, "dur": 19, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953335, "dur": 24, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953363, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953388, "dur": 18, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953408, "dur": 17, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953428, "dur": 34, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953465, "dur": 15, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953482, "dur": 17, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953502, "dur": 21, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953525, "dur": 17, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953545, "dur": 18, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953566, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953586, "dur": 17, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953605, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953627, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953647, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953650, "dur": 20, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953672, "dur": 17, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953692, "dur": 18, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953713, "dur": 17, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953733, "dur": 14, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953750, "dur": 14, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953766, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953789, "dur": 22, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953813, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953833, "dur": 17, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953853, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953872, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953890, "dur": 17, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953910, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953931, "dur": 17, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953950, "dur": 17, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953970, "dur": 21, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592953993, "dur": 17, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954013, "dur": 16, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954031, "dur": 46, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954081, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954106, "dur": 34, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954143, "dur": 20, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954165, "dur": 20, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954188, "dur": 16, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954206, "dur": 19, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954228, "dur": 22, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954252, "dur": 19, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954274, "dur": 26, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954302, "dur": 21, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954325, "dur": 17, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954345, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954365, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954367, "dur": 16, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954385, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954408, "dur": 18, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954428, "dur": 14, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954444, "dur": 19, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954466, "dur": 23, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954492, "dur": 19, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954513, "dur": 13, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954529, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954552, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954574, "dur": 17, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954594, "dur": 20, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954617, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954637, "dur": 16, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954655, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954674, "dur": 18, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954695, "dur": 14, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954712, "dur": 18, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954732, "dur": 19, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954754, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954774, "dur": 23, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954800, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954823, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954844, "dur": 17, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954863, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954884, "dur": 19, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954906, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954926, "dur": 16, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954944, "dur": 21, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954968, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592954990, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955007, "dur": 18, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955027, "dur": 3, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955031, "dur": 18, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955051, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955071, "dur": 16, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955090, "dur": 17, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955110, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955130, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955160, "dur": 24, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955186, "dur": 17, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955206, "dur": 19, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955228, "dur": 16, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955246, "dur": 21, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955269, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955291, "dur": 15, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955309, "dur": 42, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955353, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955380, "dur": 17, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955399, "dur": 26, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955428, "dur": 19, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955450, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955472, "dur": 21, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955495, "dur": 17, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955516, "dur": 19, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955537, "dur": 32, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955572, "dur": 18, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955593, "dur": 17, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955612, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955642, "dur": 18, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955663, "dur": 19, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955684, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955704, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955726, "dur": 14, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955742, "dur": 25, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955769, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955771, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955792, "dur": 19, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955814, "dur": 17, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955834, "dur": 18, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955854, "dur": 18, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955875, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955894, "dur": 19, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955915, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955938, "dur": 16, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955956, "dur": 21, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955980, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592955982, "dur": 25, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956009, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956010, "dur": 27, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956040, "dur": 13, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956056, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956085, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956109, "dur": 18, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956129, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956155, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956157, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956180, "dur": 21, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956205, "dur": 19, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956226, "dur": 18, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956247, "dur": 19, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956269, "dur": 13, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956285, "dur": 16, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956303, "dur": 19, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956326, "dur": 15, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956344, "dur": 15, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956362, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956385, "dur": 21, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956409, "dur": 37, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956449, "dur": 16, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956468, "dur": 21, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956491, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956495, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956517, "dur": 28, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956548, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956568, "dur": 57, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956628, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956652, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956672, "dur": 14, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956689, "dur": 17, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956709, "dur": 116, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956828, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956853, "dur": 20, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956877, "dur": 20, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956900, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956924, "dur": 31, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956957, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956978, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592956995, "dur": 43, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957040, "dur": 15, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957058, "dur": 96, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957157, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957178, "dur": 19, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957200, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957224, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957225, "dur": 15, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957243, "dur": 20, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957266, "dur": 19, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957287, "dur": 22, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957312, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957331, "dur": 15, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957350, "dur": 16, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957369, "dur": 78, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957450, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957472, "dur": 18, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957493, "dur": 25, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957520, "dur": 14, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957536, "dur": 91, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957630, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957657, "dur": 23, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957682, "dur": 83, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957767, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957791, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957816, "dur": 17, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957836, "dur": 80, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957918, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957942, "dur": 18, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957962, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592957984, "dur": 14, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958001, "dur": 72, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958075, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958103, "dur": 17, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958122, "dur": 19, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958149, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958228, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958251, "dur": 24, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958278, "dur": 91, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958371, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958394, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958396, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958415, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958434, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958515, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958537, "dur": 23, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958563, "dur": 19, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958585, "dur": 80, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958668, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958690, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958708, "dur": 39, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958750, "dur": 75, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958828, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958848, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958865, "dur": 18, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958885, "dur": 16, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958905, "dur": 80, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592958987, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959012, "dur": 18, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959033, "dur": 14, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959049, "dur": 16, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959067, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959147, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959168, "dur": 105, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959276, "dur": 59, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959338, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959360, "dur": 27, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959390, "dur": 18, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959413, "dur": 85, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959501, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959523, "dur": 16, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959542, "dur": 26, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959570, "dur": 82, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959655, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959680, "dur": 17, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959700, "dur": 16, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959718, "dur": 81, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959802, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959825, "dur": 20, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959848, "dur": 17, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959867, "dur": 81, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959951, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592959976, "dur": 24, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960002, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960024, "dur": 15, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960042, "dur": 82, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960126, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960148, "dur": 16, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960168, "dur": 21, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960192, "dur": 13, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960208, "dur": 74, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960286, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960310, "dur": 17, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960330, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960349, "dur": 103, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960456, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960482, "dur": 21, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960507, "dur": 20, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960531, "dur": 77, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960611, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960636, "dur": 23, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960663, "dur": 13, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960678, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960759, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960853, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960855, "dur": 26, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960883, "dur": 71, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960957, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592960982, "dur": 17, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961002, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961021, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961022, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961098, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961125, "dur": 21, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961149, "dur": 14, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961166, "dur": 87, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961254, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961275, "dur": 18, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961296, "dur": 30, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961329, "dur": 135, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961468, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961491, "dur": 20, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961514, "dur": 22, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961539, "dur": 24, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961566, "dur": 129, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961697, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961767, "dur": 18, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961787, "dur": 17, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961807, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961833, "dur": 38, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961875, "dur": 31, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961911, "dur": 30, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961943, "dur": 24, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961970, "dur": 17, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592961989, "dur": 22, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962014, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962035, "dur": 122, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962161, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962211, "dur": 35, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962249, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962251, "dur": 64, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962318, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962343, "dur": 23, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962370, "dur": 29, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962402, "dur": 18, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962422, "dur": 79, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962504, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962529, "dur": 23, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962554, "dur": 26, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962583, "dur": 19, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962605, "dur": 22, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962630, "dur": 18, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962650, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962672, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962690, "dur": 80, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962772, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962802, "dur": 21, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962826, "dur": 17, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962847, "dur": 90, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962940, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962964, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962968, "dur": 22, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962992, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592962994, "dur": 83, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963081, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963114, "dur": 27, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963145, "dur": 18, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963167, "dur": 71, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963241, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963269, "dur": 23, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963297, "dur": 24, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963324, "dur": 21, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963349, "dur": 25, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963377, "dur": 19, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963399, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963401, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963422, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963442, "dur": 70, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963515, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963537, "dur": 34, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963574, "dur": 14, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963590, "dur": 73, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963667, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963692, "dur": 19, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963713, "dur": 15, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963731, "dur": 79, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963811, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963834, "dur": 20, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963856, "dur": 13, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963872, "dur": 49, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963924, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963948, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592963950, "dur": 90, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964043, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964070, "dur": 18, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964091, "dur": 17, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964110, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964189, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964211, "dur": 16, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964230, "dur": 19, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964251, "dur": 15, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964268, "dur": 77, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964348, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964370, "dur": 18, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964391, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964412, "dur": 13, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964428, "dur": 72, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964503, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964527, "dur": 20, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964549, "dur": 28, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964580, "dur": 30, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964612, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964630, "dur": 76, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964709, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964729, "dur": 14, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964747, "dur": 30, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964780, "dur": 16, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964798, "dur": 72, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964873, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964900, "dur": 19, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964921, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592964942, "dur": 78, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965022, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965045, "dur": 19, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965066, "dur": 19, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965089, "dur": 82, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965174, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965198, "dur": 21, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965222, "dur": 14, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965239, "dur": 28, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965269, "dur": 80, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965352, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965377, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965397, "dur": 16, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965418, "dur": 74, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965494, "dur": 145, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965643, "dur": 30, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965674, "dur": 1, "ph": "X", "name": "ProcessMessages 1167", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965676, "dur": 17, "ph": "X", "name": "ReadAsync 1167", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965696, "dur": 19, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965718, "dur": 103, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965824, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965849, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965872, "dur": 17, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965892, "dur": 77, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965972, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592965994, "dur": 17, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966014, "dur": 18, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966034, "dur": 18, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966055, "dur": 77, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966134, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966158, "dur": 21, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966183, "dur": 16, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966202, "dur": 74, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966279, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966307, "dur": 22, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966332, "dur": 15, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966350, "dur": 76, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966429, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966452, "dur": 37, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966493, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966495, "dur": 47, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966546, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966548, "dur": 115, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966667, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966704, "dur": 29, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966735, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966737, "dur": 106, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966847, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966878, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966879, "dur": 27, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966911, "dur": 80, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592966994, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967023, "dur": 25, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967052, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967077, "dur": 90, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967172, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967204, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967228, "dur": 16, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967247, "dur": 77, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967326, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967353, "dur": 22, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967380, "dur": 19, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967403, "dur": 19, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967424, "dur": 73, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967499, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967520, "dur": 26, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967549, "dur": 18, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967570, "dur": 78, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967651, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967673, "dur": 16, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967692, "dur": 16, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967710, "dur": 14, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967727, "dur": 13, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967743, "dur": 85, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967830, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967854, "dur": 18, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967874, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967894, "dur": 100, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592967998, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968027, "dur": 19, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968048, "dur": 81, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968134, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968161, "dur": 25, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968188, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968190, "dur": 78, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968270, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968300, "dur": 14, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968318, "dur": 16, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968337, "dur": 106, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968447, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968478, "dur": 1, "ph": "X", "name": "ProcessMessages 1123", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968483, "dur": 86, "ph": "X", "name": "ReadAsync 1123", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968571, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968595, "dur": 21, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968620, "dur": 16, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968639, "dur": 86, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968728, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968794, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968796, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968825, "dur": 79, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968906, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968930, "dur": 15, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968947, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968950, "dur": 16, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968969, "dur": 17, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592968989, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969068, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969092, "dur": 19, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969115, "dur": 18, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969136, "dur": 83, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969223, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969248, "dur": 30, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969281, "dur": 17, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969301, "dur": 78, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969382, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969406, "dur": 22, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969430, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969432, "dur": 17, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969452, "dur": 125, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969582, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969611, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969613, "dur": 98, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969715, "dur": 19, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969737, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969768, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969792, "dur": 18, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969814, "dur": 35, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969852, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969855, "dur": 93, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969951, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969954, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969986, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592969988, "dur": 37, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970027, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970030, "dur": 88, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970121, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970151, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970153, "dur": 26, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970182, "dur": 76, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970262, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970290, "dur": 21, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970313, "dur": 19, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970335, "dur": 91, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970429, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970455, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970457, "dur": 24, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970485, "dur": 23, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970512, "dur": 20, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970536, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970562, "dur": 20, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970584, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970586, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970611, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970634, "dur": 71, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970710, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970737, "dur": 21, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970765, "dur": 19, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970787, "dur": 78, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970868, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970870, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970898, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970901, "dur": 28, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970932, "dur": 20, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592970955, "dur": 65, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971022, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971047, "dur": 18, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971068, "dur": 17, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971089, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971113, "dur": 17, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971133, "dur": 56, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971192, "dur": 24, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971220, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971239, "dur": 71, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971313, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971337, "dur": 21, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971362, "dur": 17, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971381, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971383, "dur": 79, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971466, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971489, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971518, "dur": 28, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971548, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971550, "dur": 15, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971568, "dur": 25, "ph": "X", "name": "ReadAsync 122", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971596, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971614, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971633, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971651, "dur": 89, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971744, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971768, "dur": 20, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971793, "dur": 25, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971820, "dur": 13, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971836, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971915, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971938, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971960, "dur": 22, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592971986, "dur": 19, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972008, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972030, "dur": 16, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972049, "dur": 14, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972065, "dur": 18, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972086, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972106, "dur": 75, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972184, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972209, "dur": 19, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972231, "dur": 18, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972252, "dur": 16, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972271, "dur": 23, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972297, "dur": 16, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972316, "dur": 17, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972336, "dur": 14, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972353, "dur": 14, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972370, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972387, "dur": 81, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972471, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972497, "dur": 20, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972520, "dur": 27, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972554, "dur": 25, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972582, "dur": 25, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972611, "dur": 21, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972637, "dur": 21, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972661, "dur": 30, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972693, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972714, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972716, "dur": 101, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972820, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972841, "dur": 150, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592972994, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973025, "dur": 480, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973509, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973549, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973552, "dur": 22, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973576, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973578, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973602, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973630, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973662, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973664, "dur": 20, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973688, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973711, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973714, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973736, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973759, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973761, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973783, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973785, "dur": 25, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973813, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973814, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973838, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973853, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973874, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973878, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973910, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973938, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973972, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973996, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592973998, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974020, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974023, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974046, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974048, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974070, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974093, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974095, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974117, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974142, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974144, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974169, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974192, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974194, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974219, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974220, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974248, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974280, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974283, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974314, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974339, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974341, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974365, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974395, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974425, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974426, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974458, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974485, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974512, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974515, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974546, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974548, "dur": 24, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974575, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974578, "dur": 25, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974607, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974631, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974632, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974657, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974660, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974682, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974684, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974709, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974711, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974741, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974743, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974775, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974777, "dur": 30, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974811, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974814, "dur": 25, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974841, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974843, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974869, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974871, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974905, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974909, "dur": 27, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974938, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974940, "dur": 30, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974973, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592974975, "dur": 31, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975009, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975011, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975039, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975071, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975074, "dur": 25, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975101, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975104, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975134, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975137, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975165, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975167, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975200, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975202, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975233, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975237, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975261, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975263, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975297, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975304, "dur": 44, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975352, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975355, "dur": 22, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975379, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975382, "dur": 29, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975414, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975417, "dur": 21, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975441, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975443, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975469, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975471, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975496, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975498, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975527, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975553, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975555, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975585, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975589, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975617, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975620, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975653, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975655, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975686, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975688, "dur": 24, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975715, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975720, "dur": 25, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975748, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975750, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975776, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975778, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975805, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975808, "dur": 58, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975869, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975894, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975897, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975926, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975928, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975961, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975963, "dur": 23, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592975990, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976064, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976093, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976095, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976128, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976130, "dur": 27, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976158, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976160, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976190, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976193, "dur": 28, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976224, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976226, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976255, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976257, "dur": 29, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976289, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976292, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976318, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976320, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976342, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976344, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976373, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976375, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976405, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976407, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976433, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976457, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976485, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976487, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976517, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976522, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976569, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976571, "dur": 25, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976598, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976601, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976627, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976629, "dur": 24, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976655, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976657, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976682, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976683, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976713, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976741, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976743, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976769, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976771, "dur": 18, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976792, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976818, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976820, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976846, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976874, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976876, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976897, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976900, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976924, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976952, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592976972, "dur": 5300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592982282, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592982286, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592982329, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592982332, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592982356, "dur": 640, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592983001, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592983029, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592983072, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592983103, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592983105, "dur": 286, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592983395, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592983427, "dur": 844, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592984275, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592984278, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592984304, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592984349, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592984379, "dur": 197, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592984585, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592984614, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592984618, "dur": 492, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985114, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985139, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985145, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985168, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985191, "dur": 206, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985401, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985536, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985538, "dur": 235, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985778, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985804, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985824, "dur": 162, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592985989, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986007, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986027, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986082, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986100, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986102, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986140, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986155, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986220, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986222, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986244, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986307, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986328, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986368, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986371, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986400, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986402, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986440, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986487, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986510, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986541, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986561, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986581, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986602, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986604, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986626, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986645, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986667, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986688, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986709, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986727, "dur": 104, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986834, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986852, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986875, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986893, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986926, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986944, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986964, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592986990, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987010, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987071, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987089, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987144, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987208, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987229, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987253, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987277, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987363, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987382, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987445, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987463, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987495, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987512, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987553, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987572, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987691, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987709, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987739, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987757, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987778, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987781, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987801, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987816, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987833, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987850, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987868, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987900, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987918, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987937, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592987983, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988001, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988028, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988045, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988070, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988086, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988106, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988107, "dur": 161, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988272, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988286, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988324, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988340, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988380, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988401, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988484, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988500, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988660, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988681, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988704, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988737, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592988755, "dur": 426, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989186, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989246, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989248, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989271, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989274, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989293, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989392, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989413, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989439, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989459, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989480, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989503, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989524, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989627, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989650, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989671, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989702, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989719, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989742, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989765, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989785, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989838, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989856, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989875, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989905, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989918, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989941, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989952, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592989996, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990013, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990055, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990076, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990142, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990162, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990268, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990291, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990319, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990339, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990417, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990438, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990475, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990497, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990538, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990555, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990598, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990616, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990646, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990668, "dur": 114, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990786, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990799, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990843, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990869, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990871, "dur": 17, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990891, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592990911, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991001, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991015, "dur": 622, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991640, "dur": 32, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991673, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991676, "dur": 90, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991770, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991792, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991794, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991817, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991836, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991855, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991857, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991877, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991896, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991914, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991939, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991961, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991964, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592991984, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992003, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992062, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992081, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992099, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992159, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992180, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992193, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992209, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992228, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992230, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992251, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992274, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992277, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992298, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992301, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992317, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992343, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992361, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992396, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992416, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992578, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992598, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992600, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992625, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992629, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992653, "dur": 134, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992791, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992809, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992834, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992857, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992888, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992911, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592992932, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993003, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993020, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993052, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993075, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993077, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993119, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993141, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993252, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993278, "dur": 81, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993362, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993395, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993419, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993534, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993563, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993603, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993605, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993626, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993675, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993693, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993697, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993798, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993825, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993856, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592993881, "dur": 168, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994053, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994080, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994100, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994121, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994146, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994275, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994300, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994315, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994334, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994396, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994425, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994535, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994559, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994580, "dur": 306, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994890, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994945, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994965, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592994996, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995023, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995045, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995077, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995097, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995154, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995174, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995207, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995227, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995383, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995399, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995401, "dur": 331, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995735, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995753, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995782, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995972, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592995993, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592996011, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592996067, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592996087, "dur": 203, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592996295, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592996321, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592996323, "dur": 314, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592996640, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592996664, "dur": 903, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592997571, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592997597, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403592997601, "dur": 97505, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593095121, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593095126, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593095224, "dur": 3085, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593098318, "dur": 8909, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107231, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107235, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107262, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107264, "dur": 187, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107454, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107456, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107475, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107476, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107536, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107557, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107611, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107638, "dur": 308, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107949, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107975, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593107979, "dur": 271, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593108254, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593108272, "dur": 1246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593109522, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593109571, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593109574, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593109625, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593109642, "dur": 333, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593109978, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110001, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110017, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110054, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110070, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110089, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110104, "dur": 223, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110330, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110354, "dur": 129, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110487, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110505, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110618, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110633, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110689, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110709, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110811, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110833, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110881, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593110899, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593111081, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593111098, "dur": 1145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593112246, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593112268, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593112414, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593112432, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593112636, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593112655, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593112763, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593112782, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593112925, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593112928, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593112946, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593113104, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593113131, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593113154, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593113182, "dur": 332, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593113520, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593113536, "dur": 311, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593113852, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593113871, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593114069, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593114091, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593114132, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593114152, "dur": 804, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593114960, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593114980, "dur": 320, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115303, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115322, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115485, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115504, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115605, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115626, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115672, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115691, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115694, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115723, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115742, "dur": 177, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115922, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115923, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593115945, "dur": 289, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593116238, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593116257, "dur": 988, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593117250, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593117271, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593117296, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593117299, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593117318, "dur": 493, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593117815, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593117835, "dur": 187, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593118028, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593118041, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593118132, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593118155, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593118289, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593118303, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593118430, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593118453, "dur": 1039, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593119495, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593119497, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593119517, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593119520, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593119545, "dur": 594, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593120144, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593120168, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593120326, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593120347, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593120512, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593120532, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593120534, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593120605, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593120626, "dur": 558, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593121187, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593121189, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593121208, "dur": 413, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593121624, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593121643, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593121645, "dur": 299, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593121948, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593121979, "dur": 559, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593122542, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593122564, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593122637, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593122658, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593122661, "dur": 497, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593123163, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593123204, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593123208, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593123252, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593123277, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593123279, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593123326, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593123328, "dur": 547, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593123879, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593123902, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593124151, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593124153, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593124177, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593124179, "dur": 100, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593124283, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593124303, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593124461, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593124480, "dur": 665, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125149, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125170, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125259, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125284, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125308, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125359, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125382, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125479, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125499, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125565, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125566, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125588, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125626, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125687, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125705, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125707, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125751, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125773, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125795, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125796, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125815, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125832, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125836, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125857, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125882, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125907, "dur": 16, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125927, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125946, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125965, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593125989, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126013, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126015, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126036, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126094, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126112, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126134, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126161, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126183, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126202, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126225, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126245, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126264, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126284, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126303, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126323, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126344, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126365, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126389, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126408, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126429, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126431, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126452, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126473, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126494, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126517, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126535, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126555, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126558, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126592, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126623, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126625, "dur": 28, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126657, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126683, "dur": 62, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126750, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126779, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126781, "dur": 21, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126806, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126830, "dur": 144, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593126980, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127001, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127003, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127024, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127026, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127048, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127068, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127089, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127107, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127131, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127154, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127156, "dur": 21, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127180, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127182, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127204, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127206, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127227, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127229, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127253, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127276, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127299, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127301, "dur": 434, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127740, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127767, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127772, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127799, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127834, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127836, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127863, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127865, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127892, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127894, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127917, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127919, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593127946, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593128194, "dur": 12, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593128207, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593128238, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593128261, "dur": 524188, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593652457, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593652460, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593652553, "dur": 15, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593652570, "dur": 23147, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593675735, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593675742, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593675787, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593675788, "dur": 157190, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593832988, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593832991, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593833079, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593833083, "dur": 96, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593833182, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593833184, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593833211, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593833214, "dur": 85491, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593918725, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593918742, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593918794, "dur": 40, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593918835, "dur": 19331, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593938180, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593938184, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593938259, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403593938262, "dur": 103552, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594041820, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594041823, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594041846, "dur": 22, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594041869, "dur": 18964, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594060840, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594060843, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594060949, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594060952, "dur": 1261, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594062217, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594062219, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594062245, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594062260, "dur": 38901, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594101170, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594101173, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594101233, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594101235, "dur": 110274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594211517, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594211521, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594211539, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594211544, "dur": 965, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594212515, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594212518, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594212552, "dur": 21, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594212574, "dur": 849, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594213428, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594213430, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594213462, "dur": 930, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752403594214400, "dur": 32482, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 25108, "tid": 11727, "ts": 1752403594262162, "dur": 2559, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 25108, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 25108, "tid": 8589934592, "ts": 1752403592927253, "dur": 171200, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 25108, "tid": 8589934592, "ts": 1752403593098456, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 25108, "tid": 8589934592, "ts": 1752403593098459, "dur": 1486, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 25108, "tid": 11727, "ts": 1752403594264725, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 25108, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 25108, "tid": 4294967296, "ts": 1752403592897392, "dur": 1350732, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752403592901236, "dur": 9111, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752403594248328, "dur": 4800, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752403594250922, "dur": 81, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752403594253218, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 25108, "tid": 11727, "ts": 1752403594264732, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752403592934241, "dur": 54, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752403592934332, "dur": 2118, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752403592936463, "dur": 3214, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752403592939895, "dur": 138, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752403592940034, "dur": 693, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752403592941658, "dur": 1439, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_31A4DC4E54083DC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752403592943526, "dur": 188, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_38C2F330433306EF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752403592943988, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_50E26CF31C06CD18.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752403592945764, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752403592946928, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752403592949368, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752403592949630, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752403592953530, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752403592953964, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752403592954459, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752403592963152, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Sirenix.OdinInspector.Modules.UnityMathematics.dll"}}, {"pid": 12345, "tid": 0, "ts": 1752403592940754, "dur": 33524, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752403592974300, "dur": 1239717, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752403594214018, "dur": 298, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752403594214367, "dur": 76, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752403594214700, "dur": 116, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752403594214858, "dur": 24644, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752403592940664, "dur": 33645, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592974343, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592974487, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_74CBFBDE1FABEA1C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752403592974855, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592975309, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592976373, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752403592976614, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752403592976752, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752403592976912, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752403592976990, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752403592977263, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752403592977376, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752403592977429, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592977704, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752403592977850, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11632411272353536852.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752403592978018, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6054964502542534126.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752403592978297, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592978485, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592979463, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592979678, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592979869, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592980101, "dur": 898, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite\\Editor\\Interface\\IAssetDatabase.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752403592980051, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592981134, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592981336, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592981559, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592981876, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592982062, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592982272, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592982488, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592982686, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592982873, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592983062, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592983299, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592983527, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592983732, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592983980, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592984295, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592984496, "dur": 1260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592985756, "dur": 1104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592986861, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752403592987036, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752403592987251, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752403592987903, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpatialTracking.ref.dll_61966418253E747B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752403592988127, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752403592988523, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752403592989063, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592989245, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Analytics.DataPrivacy.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752403592989407, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592989549, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Analytics.DataPrivacy.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752403592990342, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592990522, "dur": 1189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1752403592991712, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592991904, "dur": 270, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403592992474, "dur": 104061, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1752403593106392, "dur": 2459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752403593108853, "dur": 844, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403593109706, "dur": 2540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752403593112247, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403593112335, "dur": 2547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752403593114882, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403593114970, "dur": 2272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Analytics.DataPrivacy.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752403593117243, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403593117373, "dur": 2317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752403593119739, "dur": 2171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752403593121964, "dur": 2580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752403593124606, "dur": 2422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752403593127030, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403593127587, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403593127976, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403593128223, "dur": 314, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.Advertisements.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1752403593128638, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752403593128731, "dur": 1085283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592941047, "dur": 33520, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592974569, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_38C2F330433306EF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752403592975104, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_41B99F78B7E58F04.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752403592975277, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_0157079A87E77991.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752403592975328, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592976085, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_41B843628A603CA9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752403592976347, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752403592976443, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752403592976552, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752403592977121, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.SpatialTracking.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752403592977345, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592977568, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752403592977717, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752403592978252, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13069972837483359549.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752403592978352, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592978559, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592979373, "dur": 738, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ads\\Runtime\\Advertisement\\Platform\\INativePlatform.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752403592979215, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592980112, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592980433, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\Services\\GoogleFetchPurchases.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752403592980288, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592981037, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592981378, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592981848, "dur": 638, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Runtime\\2D\\Shadows\\ShadowRendering.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752403592981609, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592982505, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592982672, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592982876, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592983185, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592983463, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592983675, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592983875, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592984092, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592984750, "dur": 1020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592985771, "dur": 1469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592987241, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752403592987440, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752403592988049, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592988333, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752403592988509, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592988661, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752403592989538, "dur": 1491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752403592991083, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592991395, "dur": 2804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752403592994199, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592994298, "dur": 2948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403592997247, "dur": 111646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403593108900, "dur": 3106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Configuration.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752403593112007, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403593112070, "dur": 3265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Purchasing.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752403593115336, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403593115585, "dur": 8287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752403593123873, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403593123990, "dur": 5202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752403593129193, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752403593129278, "dur": 1084743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592940677, "dur": 33647, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592974346, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592974484, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A430C75F08F988D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752403592974947, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592975047, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_D50AA53B2959027C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752403592975146, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_EACEF57EE50C357D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752403592975316, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_CB7355221ECA10D3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752403592975423, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592975679, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_180816F22416CD1E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752403592976212, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752403592976374, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752403592976734, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752403592976909, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752403592977023, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752403592977154, "dur": 239, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752403592977421, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592977628, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752403592977726, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752403592977926, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6535061076205788085.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752403592978041, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2359241780799432324.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752403592978287, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592978461, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592979108, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592980090, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592980437, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\AAR\\Models\\GooglePurchase.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752403592980348, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592981116, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592981297, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592981475, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592981652, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592981927, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592982116, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592982361, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592983005, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592983359, "dur": 1029, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\ChangeCategoryTreeViewItem.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752403592983261, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592984570, "dur": 1190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592985760, "dur": 1290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592987051, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752403592987253, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752403592987867, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592987992, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752403592988291, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752403592988814, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752403592990199, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592990682, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752403592990890, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592990955, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752403592991710, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592992107, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592992302, "dur": 978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752403592993403, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592993533, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/IAPResolver.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752403592994365, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/IAPResolver.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752403592994816, "dur": 2426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403592997243, "dur": 104146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403593104086, "dur": 467, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 3, "ts": 1752403593104554, "dur": 1980, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 3, "ts": 1752403593106535, "dur": 112, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 3, "ts": 1752403593101391, "dur": 5256, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403593106649, "dur": 2432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752403593109084, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403593109427, "dur": 2783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752403593112263, "dur": 11992, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Telemetry.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752403593124256, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752403593124723, "dur": 4422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/IAPResolver.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752403593129199, "dur": 1084812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592940734, "dur": 33611, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592974365, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592974464, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CFC043736E130462.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592974844, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592974977, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_655979F918F27A39.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592975082, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_F2E90249D91087BE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592975304, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_8443955A624F5BB6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592975356, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_6C968D1B8DB525D3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592976212, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752403592976288, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752403592976378, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752403592976488, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752403592976717, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752403592976866, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752403592977028, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752403592977123, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752403592977254, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Analytics.DataPrivacy.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752403592977408, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592977523, "dur": 185, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752403592977727, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752403592977794, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8009191425849366242.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752403592978285, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592978520, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592979265, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592979486, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592979668, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592979843, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592980042, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592980435, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Util\\IRetryPolicy.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752403592980256, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592981008, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592981205, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592981405, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592981599, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592981986, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592982183, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592982395, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592982601, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592982794, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592982978, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592983183, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592983940, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592984252, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592984554, "dur": 1212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592985766, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592986772, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592986912, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592986968, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592987228, "dur": 1916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752403592989145, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592989274, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752403592990551, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592991159, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592991596, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592991869, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752403592992676, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592992850, "dur": 1075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752403592993926, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592994036, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592994094, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592994263, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752403592994943, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592995054, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592995239, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752403592995810, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752403592995976, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752403592996654, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403592997260, "dur": 109153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403593106419, "dur": 2236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Registration.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752403593108709, "dur": 2668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleStub.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752403593111378, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403593111507, "dur": 2996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.Stores.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752403593114553, "dur": 4136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.Codeless.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752403593118691, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403593118751, "dur": 2968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.DevX.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752403593121775, "dur": 2221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752403593123997, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403593124093, "dur": 4115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Analytics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752403593128271, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Services.Analytics.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1752403593128662, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752403593129370, "dur": 1084686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592940797, "dur": 33562, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592974368, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5160C8205B154597.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592974838, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_91A0118650474178.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592975335, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9C9702B5BC184E4F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592975538, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592976076, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592976277, "dur": 8116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752403592984549, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592984739, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592984837, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752403592985833, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592986022, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752403592986686, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592986869, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752403592987565, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592988074, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592988141, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592988373, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752403592989131, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592989299, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592989523, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752403592990091, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592990288, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592990464, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752403592991113, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592991221, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592991517, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592991594, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592991804, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592991866, "dur": 1070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752403592993018, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_E399BB15452351D2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592993349, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592993741, "dur": 1101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592994843, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752403592994975, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752403592995363, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592995534, "dur": 1710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403592997245, "dur": 109149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403593106395, "dur": 2255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.SecurityCore.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752403593108698, "dur": 2295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752403593110994, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403593111079, "dur": 2585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752403593113698, "dur": 3397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752403593117095, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403593117175, "dur": 2385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752403593119594, "dur": 3424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.Purchasing.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752403593123076, "dur": 4934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752403593128060, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752403593128142, "dur": 546, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.Purchasing.Codeless.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752403593128689, "dur": 933246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752403594061958, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752403594061939, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752403594062204, "dur": 1454, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752403594063663, "dur": 150390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592940817, "dur": 33559, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592974391, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_4CE2C9A0BAA62A6D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752403592974855, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_9A8288CF5DD2C0CE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752403592974957, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_31DFCF44808B8968.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752403592975021, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592975217, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_9EE7E77B5456B59E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752403592975299, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_18EDBD7A6FB6E440.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752403592975411, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592975615, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_0848B7F7C0C86CB6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752403592976145, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752403592976413, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752403592976672, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752403592976765, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752403592976887, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752403592977063, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1752403592977250, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752403592977388, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592977624, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752403592977813, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12583078033446571771.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752403592977949, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8786433236260407851.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752403592978303, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592978494, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592979106, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592979710, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592979889, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592980065, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592980427, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\AppleAppStore\\AppleJsonProductDescriptionsDeserializer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752403592980278, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592981032, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592981214, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592981491, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592981670, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592981874, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592982067, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592982328, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592982538, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592982726, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592982916, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592983501, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592983725, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592984032, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592984748, "dur": 1028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592985776, "dur": 1470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592987247, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752403592987417, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592987596, "dur": 1728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752403592989325, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592989386, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752403592990107, "dur": 1053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752403592991197, "dur": 1039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752403592992236, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592992349, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752403592992856, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752403592993794, "dur": 3443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403592997239, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752403592997432, "dur": 108974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403593106414, "dur": 2541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752403593108957, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403593109089, "dur": 2573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.WinRTCore.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752403593111663, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403593112138, "dur": 2604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752403593114742, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403593115303, "dur": 2351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752403593117689, "dur": 2160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752403593119882, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752403593122647, "dur": 2735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752403593125383, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403593125620, "dur": 2756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752403593128377, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403593128666, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752403593129688, "dur": 1084354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592940838, "dur": 33555, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592974402, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_0AA979E9F85FAA0B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592974904, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592975159, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_7CC460A3224BB352.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592975317, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DF2CF8DDD7D6F306.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592975419, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592975507, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DF2CF8DDD7D6F306.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592976249, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752403592976377, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1752403592976449, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1752403592976604, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752403592976759, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752403592976944, "dur": 440, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752403592977414, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592977523, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752403592977629, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752403592977931, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9254761643517088472.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752403592978308, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592978524, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592979366, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592979589, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592979777, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592979970, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592980257, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592980438, "dur": 1429, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.services.core\\Editor\\Core\\Environments\\Client\\Models\\UnityGetUserGuestProjects200Response.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752403592980438, "dur": 1620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592982059, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592982290, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592982517, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592982709, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592982933, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592983419, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592983642, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592983936, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592984127, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592984285, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592984516, "dur": 1246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592985762, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592986681, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592986827, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592986918, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752403592987650, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592987883, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_9B4CF66C9079F0F4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592988120, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592988438, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752403592989147, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592989316, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592989546, "dur": 1173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752403592990720, "dur": 720, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592991508, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592991668, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592991923, "dur": 1253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752403592993275, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592993521, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592993807, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752403592994683, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592994839, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592995003, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752403592995570, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752403592995691, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752403592996010, "dur": 1236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403592997246, "dur": 114164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403593111412, "dur": 2491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752403593113904, "dur": 694, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403593114632, "dur": 2401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752403593117033, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403593117123, "dur": 2301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752403593119425, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403593119582, "dur": 2429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752403593122058, "dur": 2517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752403593124577, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403593124705, "dur": 2480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752403593127186, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403593127766, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403593127960, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752403593128072, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752403593128283, "dur": 384, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1752403593128668, "dur": 810747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752403593939448, "dur": 163036, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752403593939417, "dur": 163069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752403594102521, "dur": 111494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592940858, "dur": 33546, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592974412, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D7C1E683AFA161F2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752403592974914, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592974992, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_549D4A511CBCEDDD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752403592975143, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592975309, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592975491, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592976248, "dur": 521, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752403592976790, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752403592977120, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752403592977390, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592978133, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8070994851108164409.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752403592978296, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592978476, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592979101, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592979842, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592980028, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592980434, "dur": 564, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Editor\\2D\\Renderer2DAnalytics.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752403592980255, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592981027, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592981209, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592981412, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592981602, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592981798, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592981991, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592982175, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592982384, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592982586, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592982779, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592982964, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592983455, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592983920, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592984122, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592984363, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592984493, "dur": 1264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592985758, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592986685, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752403592986873, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752403592988081, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityStub.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752403592988316, "dur": 912, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592989232, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityStub.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752403592989881, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592990220, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752403592990544, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752403592991359, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752403592991961, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592992578, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752403592992741, "dur": 641, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592993389, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752403592993887, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592994070, "dur": 3180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403592997250, "dur": 109185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403593106440, "dur": 2333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752403593108774, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403593108898, "dur": 2639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752403593111538, "dur": 987, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403593112533, "dur": 3004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleMacosStub.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752403593115538, "dur": 5393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403593120944, "dur": 3654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752403593124599, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403593124732, "dur": 2472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752403593127458, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403593127610, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403593127777, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403593127990, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403593128064, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1752403593128146, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Services.Core.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1752403593128275, "dur": 341, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.Purchasing.Stores.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1752403593128645, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752403593129273, "dur": 1084768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592940879, "dur": 33535, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592974423, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_12BB074DEDA731F9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752403592974847, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_250F5FCDDF66F245.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752403592974905, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592974987, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_250F5FCDDF66F245.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752403592975347, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBA2C8071FCB7521.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752403592975552, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_B981FB1168C8C93A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752403592976161, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1752403592976511, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752403592976619, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1752403592976724, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752403592976858, "dur": 276, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752403592977272, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752403592977449, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592977531, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752403592977732, "dur": 359, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752403592978153, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15569745926999849003.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752403592978327, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592978554, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592979334, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592979776, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592979951, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592980180, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592980435, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\Common\\AAR\\AndroidJavaObjectExtensions.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752403592980359, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592981113, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592981306, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592981487, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592981846, "dur": 896, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Runtime\\2D\\Light2DShape.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752403592981666, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592982753, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592982932, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592983505, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592983731, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592983959, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592984207, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592984274, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592984500, "dur": 1255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592985790, "dur": 1453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592987248, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752403592987403, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592987461, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752403592987999, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592988076, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592988158, "dur": 1270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752403592989429, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592989781, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592990140, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752403592990999, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592991289, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752403592991874, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592992096, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752403592992870, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592993025, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752403592993729, "dur": 780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592994510, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752403592994703, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752403592995304, "dur": 1947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403592997252, "dur": 109175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403593106435, "dur": 2513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752403593108985, "dur": 2398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752403593111384, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403593111452, "dur": 2797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752403593114250, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403593114379, "dur": 10663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752403593125043, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403593125332, "dur": 2699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752403593128032, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752403593128093, "dur": 597, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752403593128712, "dur": 1085301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592940910, "dur": 33513, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592974433, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_87D80D01DE8F4B44.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752403592974851, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0FEF1F0834F797BE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752403592975043, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_E78A653116C8BD22.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752403592975283, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_EEB68BA9826F7A3C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752403592975350, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_2EB828704772EA6F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752403592975497, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_2EB828704772EA6F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752403592976104, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592976158, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_0B080C2548A382FC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752403592976383, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1752403592976789, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752403592976944, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752403592977065, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1752403592977162, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1752403592977377, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592977570, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752403592977731, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752403592978067, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9054347524147197993.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752403592978291, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592978487, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592979081, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592979714, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592979897, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592980099, "dur": 1649, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Editor\\ShaderScriptableStripper.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752403592980080, "dur": 1851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592981931, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592982126, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592982465, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Editor\\Lighting\\ProbeVolume\\ProbeVolumeBuildProcessor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752403592982320, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592983005, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592983263, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592983527, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592983853, "dur": 656, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics\\Unity.Mathematics\\double2.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752403592983726, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592984632, "dur": 1132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592985765, "dur": 980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592986746, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752403592986899, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592986958, "dur": 1398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752403592988448, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752403592988652, "dur": 1176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592989836, "dur": 934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752403592990770, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592990895, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Networking.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752403592991103, "dur": 1150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Networking.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752403592992254, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592992355, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752403592993353, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592993742, "dur": 1839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592995581, "dur": 1667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403592997248, "dur": 109139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403593106398, "dur": 2268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752403593108705, "dur": 2768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752403593111474, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403593111544, "dur": 3597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752403593115142, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403593115519, "dur": 12944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.WinRTStub.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752403593128465, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403593128667, "dur": 530733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403593659424, "dur": 172448, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752403593659403, "dur": 173783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752403593834347, "dur": 200, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752403593834997, "dur": 208221, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752403594061940, "dur": 150968, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752403594061932, "dur": 150978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752403594212928, "dur": 1034, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1752403592940929, "dur": 33598, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592974534, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_597300EC37484E29.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592974901, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592975015, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_225817FABB090C56.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592975111, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_225817FABB090C56.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592975255, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_9F4DA64027D3A8AD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592975364, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_D37EEA1667D204B7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592976288, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752403592976504, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752403592977061, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1752403592977356, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592977631, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1752403592977871, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592978009, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11148342699003047676.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752403592978150, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16148781348983234511.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752403592978356, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592978570, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592979400, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592979588, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592980137, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592980430, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\AAR\\Interfaces\\IGooglePurchaseStateEnumProvider.cs"}}, {"pid": 12345, "tid": 11, "ts": 1752403592980353, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592981122, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592981322, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592981507, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592981844, "dur": 640, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing\\PostProcessing\\Editor\\EffectListEditor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1752403592981686, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592982508, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592982694, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592982887, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592983097, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592983363, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592983675, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592983887, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592984089, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592984761, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592985768, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592986687, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592986854, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592987043, "dur": 1108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592988153, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592988336, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592988702, "dur": 1073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752403592989835, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752403592990502, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592990722, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592991259, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592991766, "dur": 1687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752403592993453, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592993646, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592993845, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752403592994494, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592994693, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752403592995554, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592995711, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752403592996373, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752403592996521, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752403592996830, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403592997253, "dur": 111805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403593109066, "dur": 2774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Networking.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752403593111841, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403593111938, "dur": 13913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752403593125853, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403593125917, "dur": 3312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752403593129231, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752403593129367, "dur": 1084657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592940961, "dur": 33509, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592974473, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_76BE4E04C75E42FB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752403592975296, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592975505, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_BF2E152E6652ABDE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752403592976033, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752403592976252, "dur": 7381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752403592983713, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592983825, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592984113, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592984906, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592985775, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592986693, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752403592986846, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752403592987462, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592987536, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592987599, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTStub.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752403592987752, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTStub.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752403592988571, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592988951, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592989354, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752403592989719, "dur": 935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752403592990692, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752403592991314, "dur": 879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752403592992292, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752403592992522, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752403592993320, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592993425, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752403592993611, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752403592994338, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752403592994504, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592994564, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752403592995122, "dur": 2127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403592997249, "dur": 110070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403593107326, "dur": 6708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Threading.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752403593114035, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403593114214, "dur": 2635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Components.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752403593116850, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403593117057, "dur": 2240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752403593119299, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403593119476, "dur": 2218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Configuration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752403593121696, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403593121992, "dur": 4013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752403593126006, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403593126599, "dur": 2957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752403593129559, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752403593129679, "dur": 1084379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592940711, "dur": 33622, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592974357, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592974526, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WebGLModule.dll_7671FA4B6C66E14A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752403592975314, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592976183, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1752403592976345, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityStub.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1752403592976451, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752403592976610, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752403592976751, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752403592977271, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752403592977446, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592977569, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Analytics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1752403592977836, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12381143614081962271.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752403592978209, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13245841461569978743.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752403592978401, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592979135, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592979911, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592980118, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592980429, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\GooglePlayPurchaseCallback.cs"}}, {"pid": 12345, "tid": 13, "ts": 1752403592980314, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592981106, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592981326, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592981523, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592981710, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592981934, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592982153, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592982356, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592982545, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592982741, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592983174, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592983475, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592983879, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592984091, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592984922, "dur": 839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592985761, "dur": 1248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592987011, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752403592987211, "dur": 887, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752403592988114, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752403592989145, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592989239, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752403592989430, "dur": 1131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752403592990561, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592990686, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752403592990924, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752403592991521, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752403592991712, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752403592992519, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592992938, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752403592993250, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752403592993930, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592994238, "dur": 2997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403592997242, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752403592997511, "dur": 111435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403593108952, "dur": 2445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752403593111430, "dur": 2607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752403593114086, "dur": 2533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Device.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752403593116620, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403593116755, "dur": 8178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752403593124935, "dur": 787, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752403593125734, "dur": 3469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752403593129268, "dur": 1084783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592941034, "dur": 33498, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592974533, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_96B4457290D314C3.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592975000, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E76B39341ADBD146.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592975341, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_8C8256DD42673B8D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592975499, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_8C8256DD42673B8D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592976142, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_7C46D60DEB68F39C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592976499, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1752403592976710, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752403592976941, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752403592977055, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1752403592977267, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752403592977434, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592977559, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752403592977684, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592978183, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1317364399733402406.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752403592978287, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592978481, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592979291, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592979485, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592979702, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592979875, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592980184, "dur": 1046, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Editor\\ScriptableRendererDataEditor.cs"}}, {"pid": 12345, "tid": 14, "ts": 1752403592980092, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592981371, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592981574, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592981768, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592982128, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592982461, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592982689, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592982924, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592983362, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592983602, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592983906, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592984116, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592984308, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592984517, "dur": 1236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592985793, "dur": 888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592986684, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592986849, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752403592988331, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592988520, "dur": 998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752403592989518, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592990190, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592990694, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752403592991479, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592991778, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592991928, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592992092, "dur": 205, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592992298, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752403592993028, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592993298, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Advertisements.Editor.ref.dll_2BB1E0B786C5B90D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592993443, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752403592993650, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752403592994139, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592994273, "dur": 2981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403592997255, "dur": 109164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403593106426, "dur": 2180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleCore.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752403593108653, "dur": 2608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.SecurityStub.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752403593111262, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403593111793, "dur": 2533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Scheduler.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752403593114327, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403593114560, "dur": 2338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752403593116935, "dur": 2284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752403593119265, "dur": 2281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752403593121588, "dur": 2339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752403593123928, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403593123996, "dur": 2671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752403593126822, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403593127009, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403593127497, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1752403593127566, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403593127777, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.Purchasing.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1752403593128131, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1752403593128490, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403593128639, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752403593129211, "dur": 1084801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592941068, "dur": 33469, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592974538, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1D9B8A6D435426D1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592974980, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1D9B8A6D435426D1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592975183, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_852A485882348CB3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592975297, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_454C60EADBD45A16.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592975411, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592976154, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1752403592976478, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityStub.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752403592976608, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752403592976761, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Networking.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752403592976841, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752403592976979, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752403592977160, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1752403592977348, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592977999, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4763750419435726923.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752403592978071, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6855193988388283930.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752403592978148, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6855193988388283930.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752403592978313, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592978498, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592979442, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592979626, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592979806, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592979994, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592980187, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592980430, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.services.core\\Editor\\Core\\Environments\\Client\\Scheduler\\ThreadHelper.cs"}}, {"pid": 12345, "tid": 15, "ts": 1752403592980390, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592981147, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592981503, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592982018, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592982219, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592982436, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592982646, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592982837, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592983017, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592983271, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592983530, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592983874, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592984256, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592984482, "dur": 1292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592985774, "dur": 1684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592987459, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592987671, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592987882, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592987941, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592988190, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592988544, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592988812, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592989000, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592989210, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592989350, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592989933, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752403592990646, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592990889, "dur": 905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752403592991795, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592992047, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752403592992747, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592992872, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592993073, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752403592993848, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592994045, "dur": 3194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403592997248, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752403592997448, "dur": 108951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403593106404, "dur": 2152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752403593108558, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403593108681, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752403593110973, "dur": 2503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752403593113477, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403593113881, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752403593116410, "dur": 2254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752403593118698, "dur": 2161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752403593120861, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403593121056, "dur": 2223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752403593123281, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403593123430, "dur": 2741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Windsurf.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752403593126173, "dur": 846, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403593127342, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403593127708, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403593127999, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 15, "ts": 1752403593128278, "dur": 375, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Services.Core.Threading.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1752403593128660, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752403593129300, "dur": 1084739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592941143, "dur": 33368, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592974513, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0EE7DB51FB8BE602.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592975064, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_9F5344B509F71D5B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592975251, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_DDC7B7BE7706322C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592975329, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D18D531254CCDD3B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592976213, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752403592976795, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752403592977004, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752403592977167, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752403592977339, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752403592977404, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592977530, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752403592977765, "dur": 289, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752403592978305, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592978808, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592979605, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592979783, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592979962, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592980160, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592980434, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.services.core\\Editor\\Core\\Environments\\IEnvironmentService.cs"}}, {"pid": 12345, "tid": 16, "ts": 1752403592980362, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592981121, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592981335, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592981521, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592981708, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592981927, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592982113, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592982324, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592982617, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592982800, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592983096, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592983331, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592983737, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592983872, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592984070, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592984859, "dur": 904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592985763, "dur": 1281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592987045, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592987230, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403592987889, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592988121, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592988898, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592989247, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592989481, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403592990158, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592990224, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592990687, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592991195, "dur": 1013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403592992291, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592992520, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403592993153, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592993290, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592993512, "dur": 895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403592994408, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592994507, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592994700, "dur": 1669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403592996370, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592996484, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592996601, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403592997235, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752403592997344, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592997418, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403592997710, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403592998058, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403592998897, "dur": 74, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403592999726, "dur": 654095, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403593659729, "dur": 17192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752403593659397, "dur": 17621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752403593677791, "dur": 154071, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752403593677066, "dur": 155723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752403593833976, "dur": 226, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752403593834845, "dur": 85252, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752403593939396, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1752403593939383, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1752403593939560, "dur": 274480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752403594245929, "dur": 1883, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 25108, "tid": 11727, "ts": 1752403594265311, "dur": 10764, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 25108, "tid": 11727, "ts": 1752403594276149, "dur": 2542, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 25108, "tid": 11727, "ts": 1752403594259183, "dur": 20385, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}