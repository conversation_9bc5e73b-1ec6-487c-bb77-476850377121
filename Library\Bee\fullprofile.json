{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 25108, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 25108, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 25108, "tid": 11874, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 25108, "tid": 11874, "ts": 1752405219815853, "dur": 12, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 25108, "tid": 11874, "ts": 1752405219815879, "dur": 6, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 25108, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 25108, "tid": 1, "ts": 1752405218096140, "dur": 3023, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 25108, "tid": 1, "ts": 1752405218099179, "dur": 65261, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 25108, "tid": 1, "ts": 1752405218164442, "dur": 561275, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 25108, "tid": 11874, "ts": 1752405219815887, "dur": 15, "ph": "X", "name": "", "args": {}}, {"pid": 25108, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218096095, "dur": 29722, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218125819, "dur": 1689558, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218125828, "dur": 27, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218125859, "dur": 7, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218125867, "dur": 4007, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218129880, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218129883, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218129929, "dur": 1, "ph": "X", "name": "ProcessMessages 969", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218129932, "dur": 44, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218129979, "dur": 2, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218129983, "dur": 29, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130016, "dur": 24, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130044, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130046, "dur": 29, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130078, "dur": 25, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130106, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130108, "dur": 23, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130135, "dur": 14, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130152, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130189, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130217, "dur": 23, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130243, "dur": 19, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130265, "dur": 24, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130293, "dur": 19, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130316, "dur": 19, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130338, "dur": 22, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130362, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130364, "dur": 22, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130388, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130413, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130415, "dur": 22, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130440, "dur": 16, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130459, "dur": 15, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130478, "dur": 16, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130498, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130523, "dur": 19, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130545, "dur": 22, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130568, "dur": 2, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130571, "dur": 18, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130592, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130617, "dur": 17, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130635, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130638, "dur": 23, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130663, "dur": 19, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130685, "dur": 24, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130712, "dur": 20, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130735, "dur": 23, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130762, "dur": 23, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130786, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130788, "dur": 22, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130813, "dur": 17, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130833, "dur": 19, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130855, "dur": 13, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130870, "dur": 15, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130889, "dur": 22, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130914, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130939, "dur": 22, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130964, "dur": 18, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218130985, "dur": 20, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131008, "dur": 18, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131029, "dur": 28, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131060, "dur": 16, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131079, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131102, "dur": 19, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131123, "dur": 20, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131146, "dur": 16, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131164, "dur": 22, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131189, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131213, "dur": 17, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131233, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131254, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131277, "dur": 17, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131296, "dur": 17, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131316, "dur": 29, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131348, "dur": 19, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131371, "dur": 18, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131392, "dur": 24, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131419, "dur": 18, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131438, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131440, "dur": 22, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131465, "dur": 18, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131486, "dur": 23, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131512, "dur": 18, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131534, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131558, "dur": 18, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131579, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131600, "dur": 13, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131618, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131640, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131663, "dur": 20, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131687, "dur": 22, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131712, "dur": 20, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131734, "dur": 18, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131755, "dur": 18, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131776, "dur": 18, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131797, "dur": 19, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131819, "dur": 19, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131841, "dur": 19, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131863, "dur": 25, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131890, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131892, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131913, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131937, "dur": 26, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131967, "dur": 26, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218131997, "dur": 24, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132024, "dur": 22, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132049, "dur": 19, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132070, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132072, "dur": 38, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132113, "dur": 19, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132135, "dur": 18, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132154, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132156, "dur": 16, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132175, "dur": 16, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132194, "dur": 18, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132214, "dur": 17, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132234, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132262, "dur": 22, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132287, "dur": 18, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132306, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132308, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132329, "dur": 17, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132349, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132378, "dur": 18, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132399, "dur": 21, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132423, "dur": 19, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132444, "dur": 13, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132461, "dur": 53, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132516, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132535, "dur": 17, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132555, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132577, "dur": 17, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132596, "dur": 16, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132615, "dur": 18, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132635, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132638, "dur": 16, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132658, "dur": 16, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132676, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132705, "dur": 18, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132725, "dur": 25, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132754, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132775, "dur": 16, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132794, "dur": 16, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132812, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132834, "dur": 15, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132851, "dur": 27, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132880, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132883, "dur": 32, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132918, "dur": 15, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132935, "dur": 17, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132955, "dur": 15, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132972, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218132993, "dur": 17, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133013, "dur": 20, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133036, "dur": 19, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133058, "dur": 15, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133076, "dur": 15, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133094, "dur": 16, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133113, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133131, "dur": 18, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133152, "dur": 20, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133175, "dur": 21, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133199, "dur": 18, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133219, "dur": 2, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133222, "dur": 15, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133240, "dur": 15, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133256, "dur": 1, "ph": "X", "name": "ProcessMessages 125", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133257, "dur": 21, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133281, "dur": 18, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133302, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133327, "dur": 30, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133360, "dur": 17, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133380, "dur": 15, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133398, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133418, "dur": 16, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133438, "dur": 23, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133465, "dur": 32, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133500, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133502, "dur": 19, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133525, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133546, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133568, "dur": 20, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133590, "dur": 14, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133608, "dur": 31, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133642, "dur": 19, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133664, "dur": 18, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133684, "dur": 14, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133702, "dur": 25, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133728, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133730, "dur": 17, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133750, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133772, "dur": 14, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133789, "dur": 17, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133809, "dur": 18, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133829, "dur": 19, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133851, "dur": 17, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133870, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133872, "dur": 16, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133892, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133911, "dur": 21, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133935, "dur": 19, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133957, "dur": 22, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218133983, "dur": 26, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134013, "dur": 27, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134044, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134046, "dur": 26, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134075, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134077, "dur": 26, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134107, "dur": 27, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134137, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134164, "dur": 23, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134192, "dur": 34, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134231, "dur": 55, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134290, "dur": 19, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134312, "dur": 15, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134329, "dur": 22, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134354, "dur": 29, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134386, "dur": 17, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134406, "dur": 31, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134441, "dur": 16, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134461, "dur": 15, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134479, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134502, "dur": 16, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134521, "dur": 18, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134542, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134566, "dur": 18, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134587, "dur": 14, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134603, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134625, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134649, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134652, "dur": 18, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134675, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134695, "dur": 28, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134726, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134749, "dur": 25, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134777, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134778, "dur": 18, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134799, "dur": 22, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134823, "dur": 20, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134846, "dur": 17, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134866, "dur": 16, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134884, "dur": 16, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134902, "dur": 18, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134925, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134944, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134972, "dur": 20, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218134994, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135013, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135036, "dur": 21, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135060, "dur": 16, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135079, "dur": 18, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135099, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135126, "dur": 22, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135151, "dur": 33, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135188, "dur": 20, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135212, "dur": 17, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135231, "dur": 17, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135251, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135284, "dur": 22, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135310, "dur": 15, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135328, "dur": 18, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135349, "dur": 21, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135374, "dur": 17, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135394, "dur": 19, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135416, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135435, "dur": 28, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135465, "dur": 26, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135495, "dur": 25, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135523, "dur": 19, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135545, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135572, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135574, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135606, "dur": 25, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135634, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135661, "dur": 38, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135703, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135727, "dur": 29, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135760, "dur": 28, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135791, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135793, "dur": 31, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135826, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135828, "dur": 21, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135853, "dur": 16, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135875, "dur": 26, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135906, "dur": 26, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135933, "dur": 4, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135939, "dur": 27, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135969, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218135971, "dur": 28, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136004, "dur": 18, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136025, "dur": 17, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136046, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136077, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136081, "dur": 24, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136107, "dur": 31, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136142, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136147, "dur": 31, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136183, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136185, "dur": 204, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136392, "dur": 57, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136451, "dur": 2, "ph": "X", "name": "ProcessMessages 3524", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136454, "dur": 18, "ph": "X", "name": "ReadAsync 3524", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136476, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136511, "dur": 27, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136541, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136577, "dur": 27, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136608, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136610, "dur": 29, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136642, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136644, "dur": 45, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136693, "dur": 3, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136698, "dur": 40, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136741, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136743, "dur": 41, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136789, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136821, "dur": 27, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136853, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136855, "dur": 33, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136892, "dur": 26, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136924, "dur": 36, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136962, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136967, "dur": 21, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218136993, "dur": 24, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137021, "dur": 25, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137050, "dur": 1, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137052, "dur": 21, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137076, "dur": 26, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137107, "dur": 28, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137138, "dur": 24, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137164, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137166, "dur": 24, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137194, "dur": 38, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137237, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137275, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137277, "dur": 41, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137320, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137322, "dur": 33, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137358, "dur": 4, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137363, "dur": 26, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137393, "dur": 28, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137423, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137427, "dur": 31, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137461, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137465, "dur": 229, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137697, "dur": 2, "ph": "X", "name": "ProcessMessages 2333", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137700, "dur": 29, "ph": "X", "name": "ReadAsync 2333", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137735, "dur": 55, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137795, "dur": 35, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137833, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137836, "dur": 31, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137871, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137873, "dur": 29, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137904, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137906, "dur": 19, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137931, "dur": 1, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137933, "dur": 51, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218137988, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138031, "dur": 1, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138033, "dur": 33, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138068, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138070, "dur": 31, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138105, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138107, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138133, "dur": 25, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138162, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138277, "dur": 1, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138279, "dur": 76, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138359, "dur": 24, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138388, "dur": 29, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138420, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138422, "dur": 19, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138444, "dur": 22, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138470, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138499, "dur": 24, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138526, "dur": 26, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138556, "dur": 19, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138581, "dur": 28, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138614, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138637, "dur": 45, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138684, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138767, "dur": 24, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138794, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138796, "dur": 26, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138828, "dur": 26, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138857, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138859, "dur": 27, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138890, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138916, "dur": 17, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138936, "dur": 58, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218138998, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139035, "dur": 19, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139059, "dur": 50, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139113, "dur": 26, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139141, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139143, "dur": 24, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139170, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139173, "dur": 37, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139213, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139216, "dur": 27, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139249, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139293, "dur": 21, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139317, "dur": 23, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139344, "dur": 33, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139380, "dur": 29, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139414, "dur": 21, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139439, "dur": 24, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139468, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139493, "dur": 23, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139518, "dur": 20, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139544, "dur": 21, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139571, "dur": 18, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139594, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139615, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139617, "dur": 21, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139641, "dur": 17, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139662, "dur": 17, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139683, "dur": 17, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139703, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139725, "dur": 15, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139743, "dur": 22, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139768, "dur": 18, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139789, "dur": 36, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139828, "dur": 16, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139847, "dur": 22, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139871, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139896, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139921, "dur": 13, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218139970, "dur": 29, "ph": "X", "name": "ReadAsync 29", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140001, "dur": 2, "ph": "X", "name": "ProcessMessages 1637", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140004, "dur": 18, "ph": "X", "name": "ReadAsync 1637", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140024, "dur": 15, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140044, "dur": 16, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140062, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140085, "dur": 19, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140106, "dur": 18, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140127, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140149, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140151, "dur": 18, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140173, "dur": 13, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140189, "dur": 131, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140323, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140345, "dur": 18, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140367, "dur": 14, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140383, "dur": 28, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140415, "dur": 19, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140438, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140455, "dur": 13, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140471, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140495, "dur": 17, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140516, "dur": 17, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140536, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140556, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140581, "dur": 12, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140596, "dur": 13, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140612, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140636, "dur": 18, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140657, "dur": 17, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140677, "dur": 35, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140715, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140736, "dur": 18, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140757, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140775, "dur": 13, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140795, "dur": 16, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140814, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140832, "dur": 16, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140850, "dur": 17, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140870, "dur": 19, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140892, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140910, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140932, "dur": 15, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140950, "dur": 17, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140970, "dur": 17, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218140989, "dur": 14, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141005, "dur": 21, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141030, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141055, "dur": 18, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141076, "dur": 21, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141100, "dur": 13, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141116, "dur": 14, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141133, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141153, "dur": 18, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141174, "dur": 18, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141194, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141196, "dur": 16, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141215, "dur": 16, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141237, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141255, "dur": 14, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141273, "dur": 17, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141293, "dur": 13, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141307, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141310, "dur": 16, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141329, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141347, "dur": 27, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141378, "dur": 28, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141408, "dur": 16, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141427, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141453, "dur": 17, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141474, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141500, "dur": 65, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141568, "dur": 21, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141592, "dur": 19, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141613, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141635, "dur": 12, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141650, "dur": 28, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141680, "dur": 22, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141705, "dur": 16, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141724, "dur": 24, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141754, "dur": 18, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141774, "dur": 19, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141797, "dur": 20, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141819, "dur": 18, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141840, "dur": 16, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141860, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141879, "dur": 18, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141900, "dur": 12, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141914, "dur": 19, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141936, "dur": 15, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141953, "dur": 19, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141975, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218141996, "dur": 19, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142018, "dur": 18, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142039, "dur": 33, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142076, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142113, "dur": 19, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142135, "dur": 23, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142161, "dur": 21, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142185, "dur": 15, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142203, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142221, "dur": 16, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142240, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142243, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142269, "dur": 28, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142301, "dur": 20, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142324, "dur": 16, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142343, "dur": 13, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142359, "dur": 14, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142377, "dur": 20, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142400, "dur": 18, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142420, "dur": 18, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142441, "dur": 25, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142469, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142471, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142494, "dur": 44, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142543, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142567, "dur": 20, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142588, "dur": 2, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142591, "dur": 31, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142627, "dur": 21, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142651, "dur": 18, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142671, "dur": 18, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142693, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142716, "dur": 20, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142739, "dur": 21, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142763, "dur": 16, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142781, "dur": 15, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142800, "dur": 17, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142820, "dur": 18, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142843, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142867, "dur": 17, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142886, "dur": 17, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142906, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142927, "dur": 17, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142946, "dur": 18, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142967, "dur": 14, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218142983, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143006, "dur": 18, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143027, "dur": 19, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143049, "dur": 19, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143071, "dur": 16, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143089, "dur": 18, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143111, "dur": 16, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143130, "dur": 17, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143151, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143172, "dur": 28, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143202, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143205, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143229, "dur": 24, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143256, "dur": 16, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143278, "dur": 34, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143314, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143341, "dur": 20, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143364, "dur": 18, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143385, "dur": 16, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143404, "dur": 28, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143435, "dur": 17, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143455, "dur": 30, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143487, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143528, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143531, "dur": 18, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143552, "dur": 21, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143577, "dur": 31, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143611, "dur": 22, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143637, "dur": 29, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143670, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143690, "dur": 2, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143692, "dur": 21, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143716, "dur": 19, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143739, "dur": 16, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143758, "dur": 18, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143779, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143801, "dur": 19, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143824, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143849, "dur": 19, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143873, "dur": 23, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143899, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143923, "dur": 19, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143949, "dur": 25, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218143976, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144012, "dur": 19, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144034, "dur": 18, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144055, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144074, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144094, "dur": 15, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144113, "dur": 19, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144136, "dur": 18, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144157, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144179, "dur": 17, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144199, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144221, "dur": 21, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144246, "dur": 14, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144263, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144295, "dur": 17, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144315, "dur": 20, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144338, "dur": 22, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144364, "dur": 18, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144385, "dur": 13, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144400, "dur": 19, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144422, "dur": 21, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144446, "dur": 19, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144468, "dur": 22, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144493, "dur": 19, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144515, "dur": 14, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144531, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144558, "dur": 17, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144579, "dur": 19, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144601, "dur": 23, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144626, "dur": 18, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144647, "dur": 18, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144668, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144687, "dur": 20, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144710, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144730, "dur": 18, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144751, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144771, "dur": 15, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144790, "dur": 19, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144814, "dur": 24, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144839, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144841, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144862, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144891, "dur": 20, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144913, "dur": 16, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144933, "dur": 15, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144950, "dur": 36, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144990, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218144992, "dur": 20, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145013, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145015, "dur": 28, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145046, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145067, "dur": 25, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145095, "dur": 22, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145120, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145147, "dur": 13, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145164, "dur": 17, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145184, "dur": 16, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145203, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145222, "dur": 16, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145242, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145271, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145293, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145295, "dur": 19, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145318, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145338, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145356, "dur": 17, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145376, "dur": 13, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145392, "dur": 13, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145407, "dur": 18, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145428, "dur": 13, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145444, "dur": 24, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145470, "dur": 22, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145495, "dur": 19, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145518, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145539, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145561, "dur": 25, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145589, "dur": 15, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145607, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145627, "dur": 19, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145649, "dur": 14, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145665, "dur": 16, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145684, "dur": 20, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145706, "dur": 16, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145724, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145726, "dur": 20, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145750, "dur": 30, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145783, "dur": 15, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145800, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145818, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145839, "dur": 19, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145860, "dur": 19, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145883, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145905, "dur": 16, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145925, "dur": 13, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145941, "dur": 13, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145956, "dur": 18, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145977, "dur": 15, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145994, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218145996, "dur": 18, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146017, "dur": 30, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146050, "dur": 18, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146070, "dur": 14, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146088, "dur": 16, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146107, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146130, "dur": 17, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146150, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146174, "dur": 16, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146194, "dur": 17, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146214, "dur": 21, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146238, "dur": 20, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146261, "dur": 16, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146278, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146281, "dur": 17, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146300, "dur": 20, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146322, "dur": 13, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146338, "dur": 13, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146354, "dur": 18, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146375, "dur": 17, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146395, "dur": 15, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146414, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146436, "dur": 13, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146452, "dur": 12, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146467, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146488, "dur": 12, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146503, "dur": 13, "ph": "X", "name": "ReadAsync 6", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146519, "dur": 17, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146540, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146558, "dur": 42, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146602, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146628, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146647, "dur": 15, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146665, "dur": 14, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146682, "dur": 89, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146773, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146794, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146796, "dur": 17, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146817, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146838, "dur": 14, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146854, "dur": 19, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146877, "dur": 18, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146898, "dur": 21, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146921, "dur": 13, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146937, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218146963, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147041, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147061, "dur": 14, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147077, "dur": 17, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147097, "dur": 24, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147125, "dur": 17, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147143, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147168, "dur": 18, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147188, "dur": 19, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147210, "dur": 16, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147231, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147251, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147254, "dur": 73, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147330, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147355, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147375, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147392, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147394, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147476, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147479, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147526, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147531, "dur": 53, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147592, "dur": 35, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147632, "dur": 152, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147787, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147829, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147832, "dur": 31, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147866, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147888, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147890, "dur": 29, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218147930, "dur": 149, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148085, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148114, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148117, "dur": 33, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148151, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148153, "dur": 82, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148237, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148275, "dur": 18, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148297, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148316, "dur": 87, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148408, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148429, "dur": 22, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148456, "dur": 15, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148474, "dur": 79, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148555, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148586, "dur": 16, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148605, "dur": 20, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148630, "dur": 15, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148648, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148734, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148759, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148777, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148800, "dur": 13, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148816, "dur": 81, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148899, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148920, "dur": 27, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148951, "dur": 17, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218148973, "dur": 71, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149047, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149069, "dur": 17, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149089, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149109, "dur": 14, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149126, "dur": 76, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149205, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149225, "dur": 22, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149250, "dur": 15, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149267, "dur": 16, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149286, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149364, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149383, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149385, "dur": 17, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149406, "dur": 20, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149429, "dur": 81, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149512, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149538, "dur": 18, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149557, "dur": 2, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149560, "dur": 15, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149578, "dur": 81, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149661, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149685, "dur": 18, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149705, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149726, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149727, "dur": 86, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149816, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149837, "dur": 25, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149866, "dur": 17, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149886, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149970, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218149989, "dur": 18, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150010, "dur": 16, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150029, "dur": 16, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150048, "dur": 13, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150064, "dur": 75, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150143, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150164, "dur": 14, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150181, "dur": 17, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150202, "dur": 14, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150218, "dur": 75, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150296, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150319, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150341, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150360, "dur": 13, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150376, "dur": 77, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150457, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150480, "dur": 16, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150498, "dur": 3, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150502, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150522, "dur": 95, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150620, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150647, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150666, "dur": 18, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150686, "dur": 165, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150855, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150882, "dur": 20, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150903, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150905, "dur": 22, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150931, "dur": 15, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218150949, "dur": 69, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151020, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151022, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151043, "dur": 18, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151064, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151087, "dur": 81, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151170, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151196, "dur": 18, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151216, "dur": 13, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151232, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151249, "dur": 83, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151335, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151359, "dur": 15, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151377, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151396, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151399, "dur": 13, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151415, "dur": 77, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151494, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151513, "dur": 19, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151535, "dur": 19, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151558, "dur": 12, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151573, "dur": 75, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151650, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151688, "dur": 27, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151717, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151736, "dur": 227, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218151967, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152030, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152032, "dur": 28, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152063, "dur": 28, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152093, "dur": 38, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152136, "dur": 25, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152164, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152166, "dur": 31, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152200, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152202, "dur": 20, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152226, "dur": 25, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152258, "dur": 277, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152539, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152542, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152618, "dur": 23, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152643, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152645, "dur": 340, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218152989, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153030, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153035, "dur": 32, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153070, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153072, "dur": 26, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153101, "dur": 29, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153136, "dur": 33, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153175, "dur": 172, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153350, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153384, "dur": 25, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153414, "dur": 18, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153434, "dur": 26, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153465, "dur": 28, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153497, "dur": 34, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153534, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153536, "dur": 25, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153564, "dur": 53, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153621, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153670, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153672, "dur": 251, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153926, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153962, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218153983, "dur": 28, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154014, "dur": 26, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154045, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154068, "dur": 170, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154241, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154271, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154273, "dur": 25, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154301, "dur": 16, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154323, "dur": 132, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154458, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154485, "dur": 23, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154511, "dur": 124, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154639, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154674, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154676, "dur": 24, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154703, "dur": 26, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154732, "dur": 17, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154751, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154752, "dur": 15, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154770, "dur": 16, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154789, "dur": 15, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154806, "dur": 16, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154824, "dur": 24, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154854, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154879, "dur": 109, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218154992, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155022, "dur": 26, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155050, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155053, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155076, "dur": 3, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155081, "dur": 106, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155190, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155212, "dur": 21, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155236, "dur": 17, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155256, "dur": 18, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155276, "dur": 89, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155368, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155390, "dur": 14, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155408, "dur": 20, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155431, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155433, "dur": 87, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155524, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155546, "dur": 71, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155619, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155645, "dur": 40, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155688, "dur": 35, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155725, "dur": 13, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155741, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155820, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155845, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155846, "dur": 21, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155872, "dur": 75, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155949, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218155979, "dur": 22, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156004, "dur": 16, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156022, "dur": 76, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156101, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156132, "dur": 20, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156155, "dur": 20, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156178, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156254, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156277, "dur": 16, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156296, "dur": 16, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156315, "dur": 16, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156333, "dur": 117, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156454, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156484, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156506, "dur": 23, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156531, "dur": 88, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156623, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156656, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156658, "dur": 19, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156681, "dur": 78, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156761, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156787, "dur": 20, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156811, "dur": 26, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156841, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156867, "dur": 27, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156897, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156922, "dur": 15, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218156939, "dur": 76, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157018, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157040, "dur": 25, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157068, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157069, "dur": 20, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157092, "dur": 75, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157170, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157196, "dur": 22, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157222, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157244, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157245, "dur": 80, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157329, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157353, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157355, "dur": 27, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157384, "dur": 18, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157405, "dur": 12, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157419, "dur": 78, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157499, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157522, "dur": 15, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157539, "dur": 18, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157560, "dur": 15, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157578, "dur": 84, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157665, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157691, "dur": 21, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157716, "dur": 14, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157733, "dur": 68, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157803, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157828, "dur": 20, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157854, "dur": 17, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157874, "dur": 112, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218157989, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158013, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158036, "dur": 19, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158057, "dur": 79, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158139, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158164, "dur": 64, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158230, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158231, "dur": 63, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158297, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158324, "dur": 20, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158347, "dur": 17, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158369, "dur": 68, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158440, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158464, "dur": 20, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158487, "dur": 17, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158506, "dur": 1, "ph": "X", "name": "ProcessMessages 202", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158509, "dur": 18, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158529, "dur": 66, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158598, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158621, "dur": 18, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158643, "dur": 17, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158662, "dur": 73, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158738, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158756, "dur": 16, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158775, "dur": 13, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158791, "dur": 19, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158813, "dur": 14, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158830, "dur": 70, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158903, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158929, "dur": 22, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218158954, "dur": 75, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159033, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159056, "dur": 19, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159079, "dur": 27, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159108, "dur": 75, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159186, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159207, "dur": 28, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159239, "dur": 19, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159262, "dur": 76, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159341, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159364, "dur": 16, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159384, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159406, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159408, "dur": 71, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159482, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159504, "dur": 17, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159524, "dur": 19, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159546, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159547, "dur": 14, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159564, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159638, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159663, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159685, "dur": 14, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159702, "dur": 74, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159781, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159807, "dur": 16, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159827, "dur": 15, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159845, "dur": 75, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159923, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159947, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159970, "dur": 17, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218159990, "dur": 105, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160098, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160122, "dur": 23, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160147, "dur": 3, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160150, "dur": 18, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160171, "dur": 82, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160255, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160282, "dur": 21, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160306, "dur": 14, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160322, "dur": 74, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160398, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160420, "dur": 27, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160449, "dur": 14, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160465, "dur": 15, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160483, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160562, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160583, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160604, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160625, "dur": 12, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160640, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160719, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160739, "dur": 16, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160757, "dur": 18, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160781, "dur": 15, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160798, "dur": 80, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160882, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160903, "dur": 15, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160920, "dur": 40, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160963, "dur": 16, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160981, "dur": 13, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218160997, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161076, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161098, "dur": 17, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161117, "dur": 15, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161135, "dur": 14, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161152, "dur": 73, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161228, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161248, "dur": 16, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161267, "dur": 15, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161285, "dur": 10, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161297, "dur": 76, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161376, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161398, "dur": 16, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161418, "dur": 19, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161440, "dur": 15, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161458, "dur": 72, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161533, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161557, "dur": 29, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161589, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161590, "dur": 16, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161609, "dur": 67, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161679, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161705, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161707, "dur": 28, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161739, "dur": 69, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161811, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161837, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161859, "dur": 16, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161877, "dur": 16, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161896, "dur": 18, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161917, "dur": 20, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161940, "dur": 16, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161958, "dur": 13, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161974, "dur": 19, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218161995, "dur": 15, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162015, "dur": 81, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162098, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162120, "dur": 18, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162144, "dur": 25, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162172, "dur": 15, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162190, "dur": 70, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162263, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162287, "dur": 22, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162313, "dur": 18, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162334, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162406, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162429, "dur": 20, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162453, "dur": 20, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162476, "dur": 17, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162496, "dur": 19, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162517, "dur": 17, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162538, "dur": 19, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162560, "dur": 20, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162582, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162600, "dur": 72, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162674, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162699, "dur": 18, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162720, "dur": 18, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162741, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162743, "dur": 75, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162821, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162843, "dur": 16, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162863, "dur": 18, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162884, "dur": 14, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162901, "dur": 15, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162919, "dur": 21, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162944, "dur": 18, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162964, "dur": 18, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218162986, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163005, "dur": 77, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163086, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163105, "dur": 16, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163124, "dur": 19, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163145, "dur": 14, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163162, "dur": 72, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163236, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163261, "dur": 18, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163282, "dur": 17, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163303, "dur": 23, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163330, "dur": 21, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163353, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163355, "dur": 36, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163394, "dur": 19, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163417, "dur": 17, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163438, "dur": 77, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163518, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163545, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163547, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163573, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163592, "dur": 14, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163609, "dur": 24, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163636, "dur": 20, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163658, "dur": 15, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163677, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163694, "dur": 14, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163711, "dur": 79, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163794, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163818, "dur": 17, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163837, "dur": 31, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163871, "dur": 19, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163893, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163912, "dur": 1, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163914, "dur": 19, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163937, "dur": 15, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163957, "dur": 22, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218163981, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164062, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164081, "dur": 80, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164166, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164194, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164195, "dur": 128, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164327, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164354, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164356, "dur": 32, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164391, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164416, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164440, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164442, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164468, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164470, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164491, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164516, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164544, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164573, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164600, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164603, "dur": 19, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164626, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164650, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164652, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164677, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164679, "dur": 21, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164703, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164727, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164729, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164752, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164754, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164778, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164799, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164867, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164891, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164919, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164921, "dur": 22, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164945, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164947, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164971, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218164972, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165009, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165012, "dur": 33, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165050, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165092, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165097, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165130, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165162, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165196, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165231, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165235, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165270, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165301, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165304, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165344, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165346, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165387, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165392, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165450, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165485, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165488, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165525, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165527, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165564, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165608, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165610, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165641, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165643, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165667, "dur": 32, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165702, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165705, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165745, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165755, "dur": 30, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165789, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165830, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165833, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165873, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165876, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165919, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165920, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165961, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218165964, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166005, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166007, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166046, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166048, "dur": 30, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166082, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166084, "dur": 34, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166122, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166123, "dur": 27, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166153, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166156, "dur": 22, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166181, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166183, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166210, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166212, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166239, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166241, "dur": 25, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166270, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166272, "dur": 19, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166295, "dur": 28, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166325, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166328, "dur": 24, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166354, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166356, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166384, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166385, "dur": 36, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166426, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166428, "dur": 31, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166463, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166465, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166492, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166493, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166524, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166526, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166553, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166555, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166587, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166588, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166617, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166623, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166651, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166654, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166681, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166683, "dur": 33, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166720, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166722, "dur": 39, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166764, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166766, "dur": 29, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166797, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166799, "dur": 36, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166838, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166840, "dur": 46, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166891, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166893, "dur": 34, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166937, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166976, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218166979, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167018, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167020, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167047, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167073, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167075, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167103, "dur": 451, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167559, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167585, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167590, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167615, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167617, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167645, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167647, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167677, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167679, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167706, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167708, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167746, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167749, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167776, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167779, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167813, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167816, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167851, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167854, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167888, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218167892, "dur": 160, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168058, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168092, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168094, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168120, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168122, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168149, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168151, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168173, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168175, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168197, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168199, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168224, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168254, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168255, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168279, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168280, "dur": 17, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168300, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168301, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168323, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168344, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168368, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168372, "dur": 20, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168396, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168419, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168421, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168444, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168466, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168468, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168493, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168518, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168520, "dur": 20, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168544, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168545, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168570, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168594, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168596, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168621, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168650, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168652, "dur": 23, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168678, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168680, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168704, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168706, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168731, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168733, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168750, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168772, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168775, "dur": 21, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168799, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168802, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168823, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168825, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168856, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168879, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168902, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218168990, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218169014, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218169042, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218169065, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218169067, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218169136, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218169158, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218169181, "dur": 6005, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175195, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175199, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175226, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175229, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175251, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175253, "dur": 267, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175526, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175543, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175566, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175568, "dur": 24, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175596, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175598, "dur": 94, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175696, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218175715, "dur": 542, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218176262, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218176290, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218176315, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218176336, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218176477, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218176496, "dur": 296, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218176794, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218176812, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218176831, "dur": 219, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177053, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177079, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177099, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177131, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177153, "dur": 58, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177215, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177237, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177283, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177303, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177307, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177346, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177367, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177499, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177517, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177611, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177614, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177631, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177779, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177796, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177830, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177847, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177952, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177969, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218177997, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178018, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178020, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178052, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178073, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178075, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178095, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178117, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178136, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178155, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178158, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178179, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178200, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178279, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178297, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178319, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178336, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178395, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178415, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178434, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178462, "dur": 16, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178481, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178498, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178515, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178535, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178555, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178588, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178608, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178626, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178665, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178686, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178703, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178856, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178884, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178886, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178905, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178929, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178953, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218178987, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179011, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179031, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179155, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179174, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179213, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179229, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179263, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179279, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179402, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179418, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179442, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179462, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179488, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179512, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179539, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179541, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179561, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179578, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179633, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179650, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179677, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179693, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179713, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179750, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179771, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179787, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179805, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179825, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179846, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179869, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179895, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179897, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218179915, "dur": 165, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180083, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180105, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180124, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180143, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180230, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180246, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180287, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180310, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180329, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180350, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180370, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180396, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180418, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180434, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180556, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180574, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180608, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180626, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180649, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180669, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180687, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180741, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180760, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180775, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180834, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180850, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180926, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180944, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180976, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218180994, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181012, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181026, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181071, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181086, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181088, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181100, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181114, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181283, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181300, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181318, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181334, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181360, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181378, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181397, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181415, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181433, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181451, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181558, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181561, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181580, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181599, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181642, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181659, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181677, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181750, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181766, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181835, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181855, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181881, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181900, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181928, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181946, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181974, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218181997, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182017, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182035, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182052, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182130, "dur": 28, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182163, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182166, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182204, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182227, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182259, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182271, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182326, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182345, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182391, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182414, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182471, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182493, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182513, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182562, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182580, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182601, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182625, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182645, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182664, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182682, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182695, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182712, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182732, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182752, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218182754, "dur": 287, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183045, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183068, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183094, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183135, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183154, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183156, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183191, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183212, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183275, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183297, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183321, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183323, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183344, "dur": 4, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183349, "dur": 19, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183372, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183396, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183416, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183487, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183510, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183533, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183561, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183580, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183599, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183643, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183645, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183671, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183692, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183696, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183719, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183722, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183742, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183761, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183780, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183781, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218183803, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184006, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184030, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184051, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184075, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184096, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184290, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184313, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184314, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184437, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184440, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184464, "dur": 170, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184638, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184661, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184685, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184686, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184710, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184738, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184768, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184770, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184796, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184817, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184879, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184895, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184948, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218184965, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185002, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185027, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185029, "dur": 209, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185241, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185264, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185279, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185315, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185335, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185576, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185601, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185627, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185778, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185812, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185845, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185944, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185964, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218185982, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186112, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186135, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186160, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186192, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186218, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186280, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186304, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186379, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186424, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186492, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186519, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186551, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186575, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186609, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186631, "dur": 170, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186806, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186877, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186902, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186914, "dur": 34, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186950, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218186952, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218187155, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218187188, "dur": 272, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218187464, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218187490, "dur": 638, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218188131, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218188154, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218188202, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218188224, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218188340, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218188358, "dur": 593, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218188955, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218188999, "dur": 149, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218189152, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218189179, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218189202, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218189244, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218189261, "dur": 204, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218189469, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218189485, "dur": 312, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218189800, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218189818, "dur": 418, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218190240, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218190260, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218190262, "dur": 499942, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218690215, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218690219, "dur": 178, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218690399, "dur": 39, "ph": "X", "name": "ProcessMessages 4430", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405218690439, "dur": 354180, "ph": "X", "name": "ReadAsync 4430", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219044629, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219044632, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219044652, "dur": 16, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219044669, "dur": 32128, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219076805, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219076808, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219076879, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219076883, "dur": 163497, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219240402, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219240411, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219240508, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219240514, "dur": 69863, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219310390, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219310398, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219310443, "dur": 30, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219310474, "dur": 172147, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219482630, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219482633, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219482660, "dur": 20, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219482681, "dur": 22719, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219505412, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219505416, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219505454, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219505457, "dur": 3834, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219509301, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219509306, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219509403, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219509407, "dur": 1486, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219510902, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219510906, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219510941, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219510967, "dur": 81244, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219592219, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219592222, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219592241, "dur": 14, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219592257, "dur": 19282, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219611552, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219611556, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219611624, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219611627, "dur": 488, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219612119, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219612121, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219612150, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219612169, "dur": 63894, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219676072, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219676075, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219676109, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219676112, "dur": 904, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219677025, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219677053, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219677071, "dur": 97575, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219774661, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219774666, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219774692, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219774698, "dur": 757, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219775463, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219775469, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219775517, "dur": 32, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219775551, "dur": 1032, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219776591, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219776632, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 25108, "tid": 25769803776, "ts": 1752405219776634, "dur": 38738, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 25108, "tid": 11874, "ts": 1752405219815904, "dur": 3152, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 25108, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 25108, "tid": 21474836480, "ts": 1752405218096041, "dur": 629725, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 25108, "tid": 21474836480, "ts": 1752405218725767, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 25108, "tid": 21474836480, "ts": 1752405218725768, "dur": 148, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 25108, "tid": 11874, "ts": 1752405219819059, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 25108, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 25108, "tid": 17179869184, "ts": 1752405218092039, "dur": 1723377, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 25108, "tid": 17179869184, "ts": 1752405218092172, "dur": 3025, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 25108, "tid": 17179869184, "ts": 1752405219815422, "dur": 63, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 25108, "tid": 17179869184, "ts": 1752405219815434, "dur": 14, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 25108, "tid": 11874, "ts": 1752405219819098, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752405218126162, "dur": 1735, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405218127906, "dur": 1976, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405218130013, "dur": 124, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752405218130137, "dur": 485, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405218134971, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1752405218137199, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405218138505, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752405218138965, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1752405218139085, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405218139470, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Services.Core.Configuration.Editor.ref.dll_637774E0225B1CEC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405218139845, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Networking.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752405218140033, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Services.Core.Registration.ref.dll_C77F1856ED378310.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405218142300, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405218152879, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405218154399, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13245841461569978743.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405218161706, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleStub.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1752405218130643, "dur": 34222, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405218164884, "dur": 1611509, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405219776395, "dur": 266, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405219776798, "dur": 100, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405219776931, "dur": 56, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405219777037, "dur": 59, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405219777165, "dur": 163, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405219777369, "dur": 28065, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752405218130508, "dur": 34383, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218164978, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A430C75F08F988D8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218165379, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_9EE7E77B5456B59E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218165564, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C2741807B1FC8886.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218165716, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218165794, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_12405E7AD464376C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218165915, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_481955CAFD152279.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218166351, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218166551, "dur": 9726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218176367, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218176486, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218177122, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218177274, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218177656, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218178017, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218179050, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218179236, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_75A67EE1951F90CE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218179388, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218179654, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218180455, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218180589, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405218181026, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218181780, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218182107, "dur": 1127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1752405218183237, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218183323, "dur": 406, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218184153, "dur": 62912, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1752405218254568, "dur": 2453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218257022, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218257251, "dur": 2598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218259879, "dur": 2550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Networking.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218262430, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218262707, "dur": 2466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218265205, "dur": 2495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218267740, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218270219, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218270427, "dur": 2501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405218272960, "dur": 612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218273580, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218273685, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218273924, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218274013, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218274382, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405218274827, "dur": 1501531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218130526, "dur": 34375, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218164960, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_87D80D01DE8F4B44.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405218165180, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218165389, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_69785AF30F99F613.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405218165537, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_6C968D1B8DB525D3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405218165664, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218165766, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218165845, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_52974FDBA3F0497F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405218166155, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2804DA6C2AC419DA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405218166212, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218166522, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752405218166722, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405218166860, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Internal.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752405218167063, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405218167201, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752405218167473, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752405218167652, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405218167757, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405218167826, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405218167933, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218168465, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752405218168581, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218168689, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752405218168975, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12783842955110642247.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405218169190, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17546307333235763125.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405218169286, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218169756, "dur": 1368, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Prestige\\AbilityManager.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752405218169618, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218171181, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218171853, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218172103, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218172291, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218172486, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218172851, "dur": 658, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\AAR\\Interfaces\\IGooglePurchase.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752405218172730, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218173607, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218173815, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218174018, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218174199, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218174397, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218174646, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218174876, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218175156, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218175674, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218175871, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218176451, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218177083, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218177672, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405218178007, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405218178074, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405218178912, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218179313, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405218179780, "dur": 1611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405218181392, "dur": 959, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218182357, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405218182630, "dur": 1339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405218183970, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218184106, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218184616, "dur": 1821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218186438, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218186783, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405218186991, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405218187468, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218187647, "dur": 2177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218189825, "dur": 64812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218254647, "dur": 6426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405218261074, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218261176, "dur": 2697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405218263874, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218264304, "dur": 2551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405218266894, "dur": 2451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405218269382, "dur": 2534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405218271917, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218272094, "dur": 2704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405218274801, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405218274875, "dur": 1501463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218130545, "dur": 34361, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218164974, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_74CBFBDE1FABEA1C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405218165348, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_74CBFBDE1FABEA1C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405218165450, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_E88B09B6A6B366F7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405218165548, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_D37EEA1667D204B7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405218165898, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_9FFC13882A17C1F8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405218166347, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405218166553, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405218166855, "dur": 9043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405218175975, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218176114, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218176327, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218177075, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218177648, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405218177809, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218177899, "dur": 987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405218178887, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218179130, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405218179484, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405218180289, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218180476, "dur": 1204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405218181770, "dur": 843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405218182614, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218182757, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218182872, "dur": 486, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Updater.ref.dll_80FC5D41077E44D3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405218183360, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405218183624, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218184067, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405218184596, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218184845, "dur": 1606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218186451, "dur": 3386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218189837, "dur": 64757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218254600, "dur": 2609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405218257210, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218257292, "dur": 5387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405218262680, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218263296, "dur": 2498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405218265795, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218266248, "dur": 3330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Configuration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405218269579, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218270019, "dur": 2637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405218272657, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218272818, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218273138, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218273288, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218273406, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218273605, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218273836, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218274384, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405218274899, "dur": 1501443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218130568, "dur": 34345, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218164969, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CFC043736E130462.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405218165294, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218165528, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9C9702B5BC184E4F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405218165863, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_4B8C89D368746A27.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405218166525, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752405218166740, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405218167058, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752405218167365, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405218167476, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752405218167584, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1752405218167890, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218168512, "dur": 350, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405218168864, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405218169393, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16148781348983234511.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405218169468, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13245841461569978743.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405218169577, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218169827, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218170117, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218171020, "dur": 1624, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Abstractions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752405218172843, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HttpLogging.dll"}}, {"pid": 12345, "tid": 4, "ts": 1752405218170931, "dur": 2865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218173796, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218173997, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218174180, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218174358, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218174548, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218174732, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218174980, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218175173, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218175380, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218175570, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218175770, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218175977, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218176056, "dur": 56, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218176113, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218176329, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218177074, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218177666, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405218178141, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218178837, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218178987, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityStub.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405218179191, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityStub.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218179842, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218179953, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218180009, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Analytics.DataPrivacy.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218180655, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218180921, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405218181149, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218181942, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218182225, "dur": 2151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218184377, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218184569, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405218185166, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218185229, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218185797, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405218185946, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218186035, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218186812, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218186931, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405218187069, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218187639, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218187717, "dur": 2101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218189819, "dur": 64829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218254653, "dur": 2331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Threading.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218256985, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218257362, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218259642, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218259948, "dur": 9988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Telemetry.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218269937, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218270440, "dur": 2435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405218273355, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218273470, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218273544, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218273815, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218274172, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218274390, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405218275294, "dur": 1501073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218130589, "dur": 34330, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218164926, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5160C8205B154597.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218165235, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_31DFCF44808B8968.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218165361, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_A89A756DEE006762.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218165562, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218165714, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218165815, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_0848B7F7C0C86CB6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218165938, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_BA981B41A84C2CF5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218166136, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_5609C7B91935AD18.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218166285, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405218166483, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752405218166864, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752405218167187, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405218167471, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405218167581, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752405218167867, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405218167940, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218168588, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218168698, "dur": 248, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752405218168948, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8009191425849366242.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405218169050, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4769955788402727329.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405218169128, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218169450, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5138823106612435834.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405218169576, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218169792, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218170083, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218171061, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218171789, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.windsurf\\Editor\\SolutionProjectEntry.cs"}}, {"pid": 12345, "tid": 5, "ts": 1752405218171735, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218172608, "dur": 905, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Editor\\2D\\ShapeEditor\\Selection\\RectSelector.cs"}}, {"pid": 12345, "tid": 5, "ts": 1752405218172573, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218173675, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218174118, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218174298, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218174530, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218174728, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218174964, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218175169, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218175355, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218175529, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218175713, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218175937, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218176196, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218176348, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218177081, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218177860, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218178139, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218178409, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218178627, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218178937, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218179123, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218179252, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218179459, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Analytics.DataPrivacy.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218179680, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218180281, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218181077, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218181225, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218181537, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218182005, "dur": 844, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218182858, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218183437, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218183832, "dur": 1536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218185369, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218185478, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218185669, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218186444, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405218186605, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218187340, "dur": 2487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218189827, "dur": 64784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218254618, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleCore.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218257007, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218257134, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218259718, "dur": 5444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Analytics.DataPrivacy.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218265203, "dur": 3679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.DevX.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218268922, "dur": 2375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218271298, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218271363, "dur": 2651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/IAPResolver.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405218274045, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218274340, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.Purchasing.SecurityStub.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1752405218274397, "dur": 456127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405218730524, "dur": 1045809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218130613, "dur": 34313, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218164934, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_4CE2C9A0BAA62A6D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405218165232, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_E40AE0367B9390BA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405218165562, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218165783, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_AE6044FD003A77E7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405218165844, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218166362, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405218166529, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752405218166835, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752405218167139, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405218167409, "dur": 284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1752405218167784, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405218167851, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405218167941, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218168430, "dur": 624, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405218169420, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14919082851048885018.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405218169552, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10885039498462050167.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1752405218169831, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218170187, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218171197, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218171444, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218171716, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218171954, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218172153, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218172387, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218172643, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218172845, "dur": 630, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.services.core\\Editor\\Core\\Environments\\Client\\Http\\HttpException.cs"}}, {"pid": 12345, "tid": 6, "ts": 1752405218172845, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218173741, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218173945, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218174126, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218174318, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218174515, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218174704, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218174909, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218175104, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218175288, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218175511, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218175817, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218176488, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218177084, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218177659, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405218177928, "dur": 1253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218179264, "dur": 379, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218179650, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405218179965, "dur": 1194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218181195, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405218181554, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218182096, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218182234, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218183118, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218183202, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218183283, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/IAPResolver.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405218183537, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/IAPResolver.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218184142, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218184353, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218184560, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405218184810, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218185320, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218185535, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218186450, "dur": 3363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218189813, "dur": 64863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218254683, "dur": 2867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218257551, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218257612, "dur": 2581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218260226, "dur": 2353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218262580, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218262650, "dur": 2431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.WinRTCore.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218265085, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218265278, "dur": 4119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218269398, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218269462, "dur": 2939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.Purchasing.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405218272402, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218272470, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218272832, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218273060, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218273123, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218273355, "dur": 945, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218274378, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405218275107, "dur": 1501263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218130636, "dur": 34298, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218164942, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_0AA979E9F85FAA0B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405218165532, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBA2C8071FCB7521.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405218165769, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_D720C7052B9B17C1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405218166107, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_CB13306F2A8F47A4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405218166175, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218166434, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Internal.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752405218166724, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752405218167023, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752405218167399, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752405218167579, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.SpatialTracking.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1752405218167761, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752405218167882, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218168509, "dur": 645, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752405218169224, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8310806540965519404.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752405218169290, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15895841462048577653.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752405218169364, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218169429, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7668679593555154362.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1752405218169519, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218169953, "dur": 954, "ph": "X", "name": "File", "args": {"detail": "Assets\\BayatGames\\SaveGameFree\\Scripts\\Serializers\\SaveGameXmlSerializer.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752405218169784, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218170954, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218171642, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218171924, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218172108, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218172289, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218172493, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218172849, "dur": 651, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\AmazonAppStore\\IAmazonExtensions.cs"}}, {"pid": 12345, "tid": 7, "ts": 1752405218172745, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218173601, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218173790, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218173986, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218174188, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218174378, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218174566, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218174772, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218175010, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218175203, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218175457, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218175655, "dur": 73, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218175728, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218175970, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218176209, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218176334, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218177082, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218177672, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405218177954, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218178011, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405218178804, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218178976, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405218179405, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405218179655, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405218180292, "dur": 1145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218181449, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405218181678, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218181901, "dur": 636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405218182537, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218182854, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Services.Core.Device.ref.dll_FF9D96777752320D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405218182997, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218183106, "dur": 257, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_2EC7FF2F52ED0106.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405218183408, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218183496, "dur": 1137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218184633, "dur": 1808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218186441, "dur": 3369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218189811, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405218190048, "dur": 64531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218254588, "dur": 2625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.SecurityCore.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405218257215, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218257285, "dur": 2533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.SecurityStub.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405218259865, "dur": 2504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405218262370, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218262612, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.Codeless.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405218265116, "dur": 2578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405218267695, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218267885, "dur": 5938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405218273873, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218273974, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218274218, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405218274414, "dur": 1337748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405219612184, "dur": 163159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752405219612164, "dur": 163182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752405219775381, "dur": 886, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1752405218130658, "dur": 34286, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218164951, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D7C1E683AFA161F2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218165224, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_77DFB3B6B03AC1A3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218165276, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218165349, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_77DFB3B6B03AC1A3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218165521, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_CB7355221ECA10D3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218165871, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_65AA30764F59CCCD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218166472, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_7C46D60DEB68F39C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218166615, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752405218166819, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752405218167336, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405218167660, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752405218167880, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405218167942, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218168429, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405218168506, "dur": 446, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/IAPResolver.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405218168954, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11632411272353536852.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405218169158, "dur": 193, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9254761643517088472.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405218169353, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8070994851108164409.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405218169459, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218169544, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15915277014004750389.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405218169646, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218169897, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218171022, "dur": 827, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventSource.dll"}}, {"pid": 12345, "tid": 8, "ts": 1752405218170733, "dur": 1582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218172315, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218172524, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218172856, "dur": 659, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.services.core\\Editor\\Core\\Environments\\Client\\Models\\UnityUserOrganizationsV1OrganizationsInner.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752405218172799, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218173644, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218173964, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218174164, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218174355, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218174537, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218174720, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218174956, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218175146, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218175354, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218175539, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218175721, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218175932, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218176545, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218177092, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218177666, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218178077, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218178672, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218178941, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218179206, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218179864, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218180269, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218180345, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218181366, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218181632, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218182162, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218182455, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218182713, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218183199, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218183389, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218184050, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218184176, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218184317, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218184497, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218185082, "dur": 1363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218186445, "dur": 3361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218189808, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405218189968, "dur": 64618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218254595, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218257171, "dur": 2630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleMacosStub.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218259802, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218259862, "dur": 2653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Configuration.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218262516, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218262627, "dur": 2586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218265213, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218265274, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218267775, "dur": 4163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218271974, "dur": 2557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405218274533, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405218274695, "dur": 1501635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218130679, "dur": 34273, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218164960, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_12BB074DEDA731F9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405218165199, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218165438, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CB00B26A74D431BF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405218165580, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_BF2E152E6652ABDE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405218165809, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_DCA0F67296522345.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405218166523, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752405218166657, "dur": 231, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405218167115, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1752405218167202, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405218167281, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405218167498, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752405218167752, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405218167816, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405218167906, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218168359, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405218168500, "dur": 389, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405218168892, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405218168967, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13798033598429234560.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405218169135, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218169303, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6855193988388283930.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405218169438, "dur": 343, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6855193988388283930.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405218169782, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218170060, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218170887, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218171503, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218171835, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218172079, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218172266, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218172446, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218172850, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\GooglePlay.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752405218172701, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218173572, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218173773, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218173993, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218174195, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218174391, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218174579, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218174769, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218175146, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218175488, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218175700, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218175914, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218176578, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218177076, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218177674, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405218178024, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218178577, "dur": 1073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218179651, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218179788, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405218180059, "dur": 1289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218181349, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218181400, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218181467, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Networking.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405218181633, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218181868, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Networking.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218182393, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405218182545, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218183324, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218183521, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218184583, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405218184829, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218185440, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218185544, "dur": 903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218186448, "dur": 3378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218189826, "dur": 64793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218254627, "dur": 2661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218257294, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218257367, "dur": 3325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleStub.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218260728, "dur": 2671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218263400, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218263577, "dur": 2533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218266110, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218266423, "dur": 2475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Analytics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218268938, "dur": 2449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218271388, "dur": 693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405218272086, "dur": 2662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405218274823, "dur": 1501512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218130701, "dur": 34327, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218165029, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_38C2F330433306EF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218165442, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_5262C70EDB48BAD1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218165542, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218165661, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_153685F339F97A2F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218165766, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_BF404702D0EB3F50.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218166522, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752405218166829, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405218167082, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405218167582, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1752405218167810, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405218167898, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218168502, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405218168713, "dur": 290, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/IAPResolver.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1752405218169067, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9138376835669937927.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405218169155, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9138376835669937927.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405218169258, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6230852051441947797.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405218169339, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15569745926999849003.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405218169412, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5814885297447240720.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405218169550, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16391513446862009207.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405218169759, "dur": 1658, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Inventory\\EquipmentSlotUI.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752405218169621, "dur": 1850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218171471, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218171772, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218172301, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218172494, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218172819, "dur": 697, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\AAR\\Models\\GoogleBillingStrings.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752405218172709, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218173612, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218173800, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218173995, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218174384, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218174595, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218174886, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218175075, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218175269, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218175459, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218175732, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218175972, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218176245, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218176328, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218177098, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218177661, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218177924, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405218178508, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218178935, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218179101, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTStub.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218179343, "dur": 1117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTStub.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405218180461, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218180617, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218180859, "dur": 540, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218181421, "dur": 3007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405218184562, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218184813, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405218185440, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218185519, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218185578, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218185790, "dur": 869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405218186660, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218186780, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218186981, "dur": 1782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405218188765, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218188993, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218189102, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405218189819, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405218189944, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405218190263, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405218190594, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405218190967, "dur": 57, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405218191079, "dur": 854313, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405219050723, "dur": 26286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752405219050399, "dur": 26689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405219077452, "dur": 74, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405219077958, "dur": 233191, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405219315635, "dur": 185309, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752405219315626, "dur": 187575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405219504486, "dur": 1629, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405219506461, "dur": 86540, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405219612166, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752405219612158, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752405219612300, "dur": 613, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752405219612920, "dur": 163412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218130725, "dur": 34249, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218164980, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_76BE4E04C75E42FB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405218165543, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218165883, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_180816F22416CD1E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405218166282, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405218166647, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405218166780, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405218166940, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1752405218167171, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405218167550, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1752405218167693, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405218167787, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405218167860, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218168510, "dur": 412, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405218168931, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8542837875481535423.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405218169321, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10017656952941242526.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405218169398, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8561397339380160663.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405218169469, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11777448413204399724.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405218169731, "dur": 2332, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\UI\\SkillEnabler.cs"}}, {"pid": 12345, "tid": 11, "ts": 1752405218169581, "dur": 2619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218172200, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218172409, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218172856, "dur": 659, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Editor\\2D\\ShapeEditor\\EditablePath\\EditablePathController.cs"}}, {"pid": 12345, "tid": 11, "ts": 1752405218172609, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218173530, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218173730, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218173946, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218174138, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218174371, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218174553, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218174747, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218174954, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218175151, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218175340, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218175544, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218176105, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218176332, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218177091, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218177668, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405218177920, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405218178735, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218178849, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405218179130, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218179480, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405218179643, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218179821, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405218180561, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218180621, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405218180874, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218181110, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405218181889, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218182196, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405218182808, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218182913, "dur": 509, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405218183428, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218183542, "dur": 1075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218184617, "dur": 1819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218186437, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405218186567, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405218187039, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218187111, "dur": 2721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218189832, "dur": 64769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218254611, "dur": 2350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Registration.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405218257010, "dur": 2655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.WinRTStub.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405218259669, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218259912, "dur": 7092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405218267005, "dur": 830, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218267842, "dur": 2638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405218270481, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218270607, "dur": 2821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405218273520, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1752405218273608, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218273898, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218274379, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405218274822, "dur": 1501506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218130759, "dur": 34222, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218164987, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0EE7DB51FB8BE602.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405218165293, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218165534, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_2EB828704772EA6F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405218165839, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BD7B6B14D9E31904.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405218165962, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_A40DBDD9635E8C8D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405218166150, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_99CAB7601582A9B2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405218166495, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405218166598, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1752405218167286, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405218167379, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1752405218167767, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405218167866, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218168351, "dur": 224, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405218168598, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1752405218168839, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405218168932, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13788528405182743862.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405218169086, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13415310291297307087.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405218169158, "dur": 767, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13415310291297307087.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405218170313, "dur": 726, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1752405218169927, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218171466, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218171761, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218172336, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218172655, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218172839, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.services.core\\Editor\\Core\\Environments\\Client\\Models\\UnityGetUserGuestProjects200Response.cs"}}, {"pid": 12345, "tid": 12, "ts": 1752405218172838, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218173699, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218173898, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218174077, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218174282, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218174470, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218174698, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218174910, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218175163, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218175371, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218175555, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218175749, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218176112, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218176330, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218177078, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218177857, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405218178107, "dur": 741, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218178851, "dur": 1445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405218180296, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218180361, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405218181084, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405218181328, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218181478, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405218182233, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405218182462, "dur": 1702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405218184208, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Services.Core.Configuration.Editor.ref.dll_637774E0225B1CEC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405218184392, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218184570, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405218184873, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405218185394, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218185518, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218186444, "dur": 3373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218189818, "dur": 64850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218254673, "dur": 2637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405218257350, "dur": 2068, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405218259422, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218259628, "dur": 2769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405218262430, "dur": 2277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405218264708, "dur": 728, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218265443, "dur": 3760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405218269205, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218269457, "dur": 4619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405218274114, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218274273, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Services.Core.Components.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1752405218274395, "dur": 452280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218728736, "dur": 377, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 12, "ts": 1752405218729114, "dur": 1258, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 12, "ts": 1752405218730373, "dur": 98, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 12, "ts": 1752405218726692, "dur": 3782, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405218730475, "dur": 1045884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218130776, "dur": 34215, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218164999, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WebGLModule.dll_7671FA4B6C66E14A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405218165188, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218165561, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_C7CE81B115F5925C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405218165926, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_CF6405559F39D8E3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405218166459, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405218166528, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405218167105, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1752405218167294, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405218167467, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1752405218167659, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1752405218167858, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405218167913, "dur": 462, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218168376, "dur": 495, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405218168873, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/IAPResolver.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405218169336, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1548875128877353482.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405218169414, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1317364399733402406.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405218169624, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218169865, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218170820, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218171585, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218171963, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218172233, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218172445, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218172847, "dur": 652, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\UDP\\UDPReflectionUtil.cs"}}, {"pid": 12345, "tid": 13, "ts": 1752405218172655, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218173531, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218173727, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218173918, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218174101, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218174283, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218174474, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218174695, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218174924, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218175365, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218175554, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218175742, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218176116, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218176333, "dur": 738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218177101, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218177671, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405218177858, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405218178507, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218178913, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405218179242, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405218179903, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218180239, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405218180503, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405218181352, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405218181558, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405218182116, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405218182572, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218182701, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218182847, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218182965, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218183280, "dur": 654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405218183987, "dur": 897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405218184884, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218185484, "dur": 959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218186443, "dur": 3372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218189816, "dur": 64747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218254578, "dur": 2897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405218257477, "dur": 656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218258139, "dur": 2696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Scheduler.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405218260872, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Device.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405218263136, "dur": 3267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218266413, "dur": 7398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405218273812, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218274169, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405218274399, "dur": 776005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405219050424, "dur": 186861, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1752405219050406, "dur": 188585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405219240577, "dur": 492, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405219241827, "dur": 241516, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405219509702, "dur": 167074, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1752405219509690, "dur": 167088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1752405219676796, "dur": 1018, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1752405219677820, "dur": 98578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218130804, "dur": 34195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218165007, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_597300EC37484E29.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218165065, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218165353, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_E2923F1ADB85ED4A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218165485, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_E5472DD94A927AAD.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218165584, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_982C2588BACDC823.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218165957, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_D024D4134FAF7A85.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218166208, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405218166488, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405218166616, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1752405218166859, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405218167170, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1752405218167234, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Networking.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405218167340, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1752405218167450, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1752405218167668, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1752405218167823, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405218167892, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218168359, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405218168519, "dur": 456, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405218168976, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10376552923787358332.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405218169040, "dur": 467, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10376552923787358332.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405218169546, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218169962, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218171423, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Xml.dll"}}, {"pid": 12345, "tid": 14, "ts": 1752405218170799, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218172208, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218172422, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218172846, "dur": 645, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Editor\\2D\\LightBatchingDebugger\\LightBatchingDebugger.cs"}}, {"pid": 12345, "tid": 14, "ts": 1752405218172640, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218173493, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218173703, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218173915, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218174093, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218174269, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218174469, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218174647, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218174851, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218175054, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218175245, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218175445, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218175621, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218175689, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218175908, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218176511, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218177086, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218177664, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218177935, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405218178628, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218178794, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218179076, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218179313, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218179407, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218179670, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405218180323, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218180626, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218180879, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218181146, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405218181771, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218181871, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405218182370, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218182453, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218182646, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218182939, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218182997, "dur": 790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405218183788, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218183927, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218184628, "dur": 1814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218186442, "dur": 3366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218189815, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405218190041, "dur": 67003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218257051, "dur": 2915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.Stores.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405218259967, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218260038, "dur": 2232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405218262338, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Components.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405218264622, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218264825, "dur": 3184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Windsurf.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405218268010, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218268143, "dur": 2481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405218270667, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405218273005, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218273216, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218273540, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218273850, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218274186, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218274300, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405218274410, "dur": 1235284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405219509718, "dur": 272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1752405219509696, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1752405219510027, "dur": 1639, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1752405219511676, "dur": 264708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218130824, "dur": 34188, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218165018, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_96B4457290D314C3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405218165545, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218165656, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_454C60EADBD45A16.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405218165741, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_BFFD438EDF64A060.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405218166379, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218166433, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1752405218166654, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405218166774, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityStub.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1752405218166972, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1752405218167066, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405218167299, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405218167575, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1752405218167867, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218168353, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405218168679, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1752405218168852, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405218168996, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3037372816226278216.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405218169159, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12176746820506266677.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405218169479, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5674956039949290316.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405218169570, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1550051217536999949.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405218169758, "dur": 1088, "ph": "X", "name": "File", "args": {"detail": "Assets\\LeanTween\\Examples\\Scripts\\PathSplinePerformance.cs"}}, {"pid": 12345, "tid": 15, "ts": 1752405218169670, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218171339, "dur": 659, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.CookiePolicy.dll"}}, {"pid": 12345, "tid": 15, "ts": 1752405218170940, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218172267, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218172446, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218172853, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\MetricizedGooglePlayStoreExtensions.cs"}}, {"pid": 12345, "tid": 15, "ts": 1752405218172672, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218173515, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218173724, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218173930, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218174113, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218174299, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218174499, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218174719, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218174938, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218175158, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218175346, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218175542, "dur": 164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218175706, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218175920, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218176621, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218177087, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218177655, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405218177845, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405218178291, "dur": 1056, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218179347, "dur": 931, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218180334, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405218180560, "dur": 1051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218181612, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218181810, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218182318, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218182656, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405218183386, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218184150, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218184622, "dur": 1812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218186435, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405218186616, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218187403, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405218187592, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218188251, "dur": 1571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218189822, "dur": 64805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218254634, "dur": 2810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218257445, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218257766, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218260440, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218260980, "dur": 2397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218263378, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218263443, "dur": 2433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218265913, "dur": 3259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218269173, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218269236, "dur": 2708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218271945, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218272151, "dur": 2996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405218275151, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405218275262, "dur": 1501113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218130846, "dur": 34173, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218165025, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1D9B8A6D435426D1.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405218165530, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_8C8256DD42673B8D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405218165718, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_93F6D81CACE8D1CE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405218166221, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218166350, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218166715, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218166785, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218167196, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218167396, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218167788, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218167896, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218168369, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218168469, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218168676, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/IAPResolver.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1752405218168823, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218168977, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3755261912168333632.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218169054, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17839364298002531413.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218169208, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1930223300244423128.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218169286, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6832337476128879279.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218169416, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1626365193864338622.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218169485, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13069972837483359549.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405218169602, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218170305, "dur": 636, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Text.Json.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752405218169883, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218171335, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218171572, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218171896, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218172079, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218172261, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218172439, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218172855, "dur": 659, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.services.core\\Editor\\Core\\Environments\\IEnvironmentFetcher.cs"}}, {"pid": 12345, "tid": 16, "ts": 1752405218172754, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218173613, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218173799, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218173985, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218174160, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218174373, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218174574, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218174768, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218174971, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218175209, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218175430, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218175613, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218175755, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218176115, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218176358, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218177080, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218177650, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405218177927, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218178018, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218178847, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405218179277, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218180287, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218180694, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405218180901, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218183180, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218183303, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405218183499, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218184327, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405218184579, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218185548, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218185607, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405218185747, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218186111, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218186440, "dur": 493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218186934, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405218187126, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218187634, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218187715, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218187943, "dur": 1869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218189812, "dur": 67245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218257059, "dur": 2794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218259890, "dur": 2601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Purchasing.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218262526, "dur": 2523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218265050, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218265303, "dur": 2527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218267831, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405218268140, "dur": 3762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218271950, "dur": 2719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405218274756, "dur": 1501571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405219814005, "dur": 1790, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 25108, "tid": 11874, "ts": 1752405219819158, "dur": 18, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 25108, "tid": 11874, "ts": 1752405219819202, "dur": 5677, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 25108, "tid": 11874, "ts": 1752405219815867, "dur": 9043, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}