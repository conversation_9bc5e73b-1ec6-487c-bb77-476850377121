{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 25108, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 25108, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 25108, "tid": 11895, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 25108, "tid": 11895, "ts": 1752405562181981, "dur": 839, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 25108, "tid": 11895, "ts": 1752405562187178, "dur": 1081, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 25108, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 25108, "tid": 1, "ts": 1752405560613862, "dur": 7593, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 25108, "tid": 1, "ts": 1752405560621469, "dur": 77963, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 25108, "tid": 1, "ts": 1752405560699454, "dur": 559109, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 25108, "tid": 11895, "ts": 1752405562188265, "dur": 15, "ph": "X", "name": "", "args": {}}, {"pid": 25108, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560605831, "dur": 86, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560605919, "dur": 1566276, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560606750, "dur": 3108, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560609866, "dur": 1410, "ph": "X", "name": "ProcessMessages 20490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560611281, "dur": 905, "ph": "X", "name": "ReadAsync 20490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612190, "dur": 14, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612206, "dur": 301, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612511, "dur": 4, "ph": "X", "name": "ProcessMessages 8516", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612516, "dur": 50, "ph": "X", "name": "ReadAsync 8516", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612569, "dur": 1, "ph": "X", "name": "ProcessMessages 3037", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612572, "dur": 70, "ph": "X", "name": "ReadAsync 3037", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612644, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612713, "dur": 1, "ph": "X", "name": "ProcessMessages 1337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612715, "dur": 74, "ph": "X", "name": "ReadAsync 1337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612791, "dur": 66, "ph": "X", "name": "ReadAsync 1158", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612860, "dur": 68, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560612931, "dur": 540, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613473, "dur": 1, "ph": "X", "name": "ProcessMessages 1291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613475, "dur": 141, "ph": "X", "name": "ReadAsync 1291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613619, "dur": 4, "ph": "X", "name": "ProcessMessages 8569", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613624, "dur": 30, "ph": "X", "name": "ReadAsync 8569", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613658, "dur": 33, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613697, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613715, "dur": 31, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613748, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613750, "dur": 32, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613783, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613785, "dur": 15, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613802, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613855, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613856, "dur": 24, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613883, "dur": 40, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613925, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613956, "dur": 24, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560613983, "dur": 25, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614010, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614038, "dur": 18, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614058, "dur": 19, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614081, "dur": 17, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614100, "dur": 19, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614122, "dur": 15, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614139, "dur": 18, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614159, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614183, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614210, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614237, "dur": 24, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614263, "dur": 18, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614284, "dur": 34, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614320, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614339, "dur": 15, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614357, "dur": 15, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614374, "dur": 18, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614394, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614413, "dur": 16, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614431, "dur": 23, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614457, "dur": 21, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614480, "dur": 13, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614497, "dur": 16, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614515, "dur": 24, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614541, "dur": 21, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614565, "dur": 28, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614598, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614679, "dur": 1, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614680, "dur": 19, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614702, "dur": 26, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614731, "dur": 22, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614756, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614776, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614795, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614814, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614816, "dur": 16, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614835, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614855, "dur": 20, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614878, "dur": 14, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614895, "dur": 25, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614922, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614946, "dur": 18, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614966, "dur": 29, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560614999, "dur": 24, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615025, "dur": 20, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615048, "dur": 52, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615103, "dur": 26, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615132, "dur": 1, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615134, "dur": 25, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615162, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615180, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615203, "dur": 22, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615227, "dur": 21, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615251, "dur": 29, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615283, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615332, "dur": 22, "ph": "X", "name": "ReadAsync 1016", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615357, "dur": 18, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615378, "dur": 16, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615396, "dur": 15, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615413, "dur": 18, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615433, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615456, "dur": 28, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615488, "dur": 26, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615516, "dur": 31, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615550, "dur": 16, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615568, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615587, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615605, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615623, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615625, "dur": 15, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615645, "dur": 22, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615669, "dur": 16, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615687, "dur": 25, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615714, "dur": 13, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615730, "dur": 18, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615750, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615843, "dur": 1, "ph": "X", "name": "ProcessMessages 1979", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615844, "dur": 20, "ph": "X", "name": "ReadAsync 1979", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615867, "dur": 13, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615883, "dur": 16, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615901, "dur": 16, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615919, "dur": 14, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615936, "dur": 17, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615956, "dur": 17, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615975, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560615996, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616015, "dur": 16, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616033, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616057, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616077, "dur": 27, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616107, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616109, "dur": 37, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616149, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616150, "dur": 83, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616239, "dur": 38, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616278, "dur": 1, "ph": "X", "name": "ProcessMessages 1259", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616279, "dur": 18, "ph": "X", "name": "ReadAsync 1259", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616300, "dur": 17, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616319, "dur": 17, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616339, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616366, "dur": 14, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616383, "dur": 24, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616410, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616438, "dur": 33, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616474, "dur": 22, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616499, "dur": 82, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616585, "dur": 22, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616609, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616630, "dur": 16, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616649, "dur": 16, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616668, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616693, "dur": 19, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616715, "dur": 16, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616734, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616753, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616774, "dur": 16, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616793, "dur": 15, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616811, "dur": 18, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616831, "dur": 14, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616849, "dur": 19, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616870, "dur": 26, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616900, "dur": 21, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616923, "dur": 41, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616968, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560616970, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617014, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617016, "dur": 37, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617056, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617058, "dur": 39, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617101, "dur": 52, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617155, "dur": 14, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617172, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617205, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617236, "dur": 24, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617263, "dur": 23, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617289, "dur": 31, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617325, "dur": 19, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617348, "dur": 29, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617379, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617401, "dur": 20, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617424, "dur": 18, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617445, "dur": 17, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617465, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617489, "dur": 14, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617505, "dur": 29, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617536, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617539, "dur": 28, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617571, "dur": 32, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617606, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617608, "dur": 36, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617648, "dur": 43, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617696, "dur": 24, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617724, "dur": 32, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617761, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617810, "dur": 40, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617854, "dur": 38, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617897, "dur": 2, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617901, "dur": 34, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560617938, "dur": 65, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618006, "dur": 53, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618065, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618068, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618141, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618144, "dur": 44, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618194, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618196, "dur": 47, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618248, "dur": 42, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618293, "dur": 52, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618351, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618425, "dur": 1, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618427, "dur": 30, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618459, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618461, "dur": 27, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618490, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618492, "dur": 22, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618518, "dur": 39, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618562, "dur": 34, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618599, "dur": 17, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618618, "dur": 20, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618641, "dur": 19, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618663, "dur": 14, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618679, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618696, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618740, "dur": 35, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618778, "dur": 164, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618945, "dur": 49, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618995, "dur": 1, "ph": "X", "name": "ProcessMessages 3082", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560618997, "dur": 20, "ph": "X", "name": "ReadAsync 3082", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619019, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619021, "dur": 86, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619110, "dur": 36, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619150, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619183, "dur": 63, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619249, "dur": 254, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619505, "dur": 57, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619564, "dur": 2, "ph": "X", "name": "ProcessMessages 3048", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619566, "dur": 88, "ph": "X", "name": "ReadAsync 3048", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619659, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619703, "dur": 1, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619705, "dur": 26, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619736, "dur": 22, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619760, "dur": 65, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619828, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619830, "dur": 38, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619870, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619872, "dur": 103, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619977, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560619979, "dur": 37, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620018, "dur": 1, "ph": "X", "name": "ProcessMessages 1010", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620020, "dur": 37, "ph": "X", "name": "ReadAsync 1010", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620061, "dur": 27, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620098, "dur": 51, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620156, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620215, "dur": 40, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620258, "dur": 19, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620279, "dur": 15, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620297, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620318, "dur": 20, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620340, "dur": 18, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620361, "dur": 19, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620382, "dur": 74, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620460, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620462, "dur": 77, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620542, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620545, "dur": 67, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620617, "dur": 75, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620694, "dur": 1, "ph": "X", "name": "ProcessMessages 1215", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620696, "dur": 62, "ph": "X", "name": "ReadAsync 1215", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620762, "dur": 125, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620890, "dur": 2, "ph": "X", "name": "ProcessMessages 1948", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620893, "dur": 75, "ph": "X", "name": "ReadAsync 1948", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560620971, "dur": 63, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621038, "dur": 70, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621112, "dur": 1, "ph": "X", "name": "ProcessMessages 1215", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621113, "dur": 70, "ph": "X", "name": "ReadAsync 1215", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621187, "dur": 43, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621235, "dur": 31, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621268, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621270, "dur": 41, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621313, "dur": 24, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621341, "dur": 22, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621368, "dur": 16, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621388, "dur": 32, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621422, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621423, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621450, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621452, "dur": 123, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621577, "dur": 2, "ph": "X", "name": "ProcessMessages 1645", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621579, "dur": 35, "ph": "X", "name": "ReadAsync 1645", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621617, "dur": 23, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621642, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621644, "dur": 23, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621669, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621671, "dur": 21, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621694, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621718, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621740, "dur": 3, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621744, "dur": 15, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621762, "dur": 41, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621807, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621833, "dur": 16, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621852, "dur": 19, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621874, "dur": 23, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621901, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621923, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621945, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560621950, "dur": 147, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622101, "dur": 147, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622250, "dur": 1, "ph": "X", "name": "ProcessMessages 2373", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622252, "dur": 19, "ph": "X", "name": "ReadAsync 2373", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622274, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622295, "dur": 38, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622336, "dur": 24, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622364, "dur": 23, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622394, "dur": 23, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622419, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622421, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622440, "dur": 28, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622471, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622488, "dur": 18, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622509, "dur": 19, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622531, "dur": 18, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622551, "dur": 23, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622577, "dur": 15, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622594, "dur": 46, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622643, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622662, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622683, "dur": 16, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622701, "dur": 19, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622722, "dur": 16, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622741, "dur": 18, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622762, "dur": 15, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622779, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622800, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622823, "dur": 19, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622845, "dur": 17, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622865, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622886, "dur": 16, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622905, "dur": 14, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622922, "dur": 15, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622939, "dur": 20, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622962, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560622981, "dur": 16, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623000, "dur": 17, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623019, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623047, "dur": 31, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623082, "dur": 29, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623114, "dur": 27, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623144, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623167, "dur": 21, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623191, "dur": 18, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623211, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623213, "dur": 17, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623232, "dur": 18, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623253, "dur": 16, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623272, "dur": 18, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623292, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623322, "dur": 17, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623341, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623360, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623384, "dur": 15, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623402, "dur": 14, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623418, "dur": 40, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623460, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623484, "dur": 18, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623504, "dur": 19, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623527, "dur": 16, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623546, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623565, "dur": 16, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623584, "dur": 31, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623617, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623638, "dur": 16, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623657, "dur": 16, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623675, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623693, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623713, "dur": 28, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623744, "dur": 18, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623765, "dur": 28, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623796, "dur": 39, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623839, "dur": 18, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623860, "dur": 16, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623879, "dur": 18, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623899, "dur": 12, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623914, "dur": 24, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623940, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623960, "dur": 20, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623982, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560623985, "dur": 33, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624021, "dur": 116, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624140, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624142, "dur": 42, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624186, "dur": 1, "ph": "X", "name": "ProcessMessages 1564", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624187, "dur": 22, "ph": "X", "name": "ReadAsync 1564", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624213, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624235, "dur": 25, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624263, "dur": 25, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624291, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624311, "dur": 34, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624350, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624388, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624390, "dur": 34, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624426, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624429, "dur": 40, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624471, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624474, "dur": 24, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624501, "dur": 1, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624503, "dur": 30, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624537, "dur": 40, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624579, "dur": 1, "ph": "X", "name": "ProcessMessages 1198", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624581, "dur": 29, "ph": "X", "name": "ReadAsync 1198", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624615, "dur": 25, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624644, "dur": 27, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624676, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624705, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624707, "dur": 26, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624736, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624738, "dur": 21, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624763, "dur": 27, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624793, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624795, "dur": 29, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624826, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624828, "dur": 24, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624857, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624889, "dur": 2, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624892, "dur": 23, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624919, "dur": 25, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624948, "dur": 21, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624971, "dur": 17, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560624991, "dur": 17, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625010, "dur": 194, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625208, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625279, "dur": 1, "ph": "X", "name": "ProcessMessages 969", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625281, "dur": 30, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625314, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625316, "dur": 27, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625344, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625347, "dur": 31, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625381, "dur": 27, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625410, "dur": 1, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625413, "dur": 44, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625459, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625461, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625495, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625497, "dur": 24, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625522, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625524, "dur": 26, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625553, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625579, "dur": 27, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625608, "dur": 2, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625611, "dur": 22, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625637, "dur": 56, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625696, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625718, "dur": 53, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625774, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625808, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625810, "dur": 26, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625837, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625839, "dur": 25, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625868, "dur": 79, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625949, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625951, "dur": 24, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560625979, "dur": 55, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626037, "dur": 55, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626096, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626128, "dur": 23, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626154, "dur": 18, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626175, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626202, "dur": 42, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626248, "dur": 17, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626267, "dur": 22, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626291, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626313, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626333, "dur": 16, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626351, "dur": 16, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626370, "dur": 17, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626390, "dur": 30, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626422, "dur": 23, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626447, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626467, "dur": 22, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626491, "dur": 22, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626516, "dur": 16, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626534, "dur": 19, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626555, "dur": 16, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626574, "dur": 15, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626591, "dur": 24, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626617, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626646, "dur": 29, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626679, "dur": 18, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626699, "dur": 33, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626734, "dur": 20, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626758, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626784, "dur": 20, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626808, "dur": 32, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626843, "dur": 25, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626870, "dur": 48, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626920, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626922, "dur": 25, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626950, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626952, "dur": 31, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560626987, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627013, "dur": 27, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627044, "dur": 37, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627083, "dur": 26, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627111, "dur": 21, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627134, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627155, "dur": 57, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627215, "dur": 25, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627243, "dur": 19, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627265, "dur": 21, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627288, "dur": 18, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627308, "dur": 17, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627328, "dur": 17, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627347, "dur": 43, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627395, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627426, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627428, "dur": 29, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627460, "dur": 26, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627488, "dur": 16, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627507, "dur": 23, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627532, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627534, "dur": 23, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627561, "dur": 17, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627581, "dur": 17, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627600, "dur": 20, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627622, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627639, "dur": 23, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627667, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627689, "dur": 17, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627709, "dur": 22, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627734, "dur": 17, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627753, "dur": 15, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627771, "dur": 24, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627797, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627823, "dur": 15, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627840, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627843, "dur": 28, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627873, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627897, "dur": 63, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627963, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560627989, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628014, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628032, "dur": 19, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628053, "dur": 149, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628207, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628239, "dur": 20, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628262, "dur": 21, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628285, "dur": 15, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628303, "dur": 18, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628323, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628343, "dur": 17, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628363, "dur": 18, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628383, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628400, "dur": 103, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628506, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628530, "dur": 26, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628559, "dur": 16, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628578, "dur": 17, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628597, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628617, "dur": 22, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628641, "dur": 16, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628659, "dur": 17, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628680, "dur": 19, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628701, "dur": 15, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628721, "dur": 92, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628815, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628835, "dur": 18, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628855, "dur": 17, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628875, "dur": 15, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628892, "dur": 104, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560628998, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629019, "dur": 15, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629037, "dur": 24, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629063, "dur": 26, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629092, "dur": 101, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629195, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629222, "dur": 13, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629237, "dur": 12, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629253, "dur": 16, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629271, "dur": 84, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629357, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629379, "dur": 16, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629399, "dur": 17, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629418, "dur": 14, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629435, "dur": 83, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629521, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629546, "dur": 30, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629578, "dur": 16, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629598, "dur": 82, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629682, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629706, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629724, "dur": 22, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629750, "dur": 13, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629765, "dur": 88, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629855, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629881, "dur": 16, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629899, "dur": 15, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629917, "dur": 17, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560629937, "dur": 87, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630026, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630049, "dur": 22, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630075, "dur": 16, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630095, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630177, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630196, "dur": 32, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630231, "dur": 14, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630248, "dur": 82, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630333, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630355, "dur": 15, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630372, "dur": 32, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630407, "dur": 1, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630409, "dur": 17, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630430, "dur": 89, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630524, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630547, "dur": 16, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630567, "dur": 16, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630585, "dur": 13, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630601, "dur": 81, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630685, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630711, "dur": 22, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630736, "dur": 13, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630751, "dur": 84, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630838, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630859, "dur": 15, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630876, "dur": 24, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630903, "dur": 14, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560630919, "dur": 82, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631004, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631022, "dur": 20, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631046, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631068, "dur": 14, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631086, "dur": 80, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631168, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631190, "dur": 15, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631207, "dur": 15, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631224, "dur": 45, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631272, "dur": 13, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631288, "dur": 84, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631374, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631399, "dur": 19, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631421, "dur": 53, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631477, "dur": 14, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631493, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631495, "dur": 83, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631581, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631601, "dur": 17, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631620, "dur": 19, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631642, "dur": 14, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631659, "dur": 88, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631751, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631782, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631802, "dur": 14, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631819, "dur": 84, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631906, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631940, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631942, "dur": 27, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631971, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560631991, "dur": 108, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632102, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632123, "dur": 14, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632140, "dur": 17, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632159, "dur": 15, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632177, "dur": 89, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632269, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632291, "dur": 15, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632309, "dur": 17, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632329, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632349, "dur": 91, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632442, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632464, "dur": 17, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632484, "dur": 27, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632513, "dur": 90, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632605, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632625, "dur": 17, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632644, "dur": 13, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632659, "dur": 12, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632674, "dur": 15, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632691, "dur": 82, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632775, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632796, "dur": 16, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632815, "dur": 18, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632835, "dur": 15, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632853, "dur": 89, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632945, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632968, "dur": 21, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560632992, "dur": 19, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633015, "dur": 102, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633119, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633140, "dur": 14, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633156, "dur": 29, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633189, "dur": 18, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633210, "dur": 110, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633323, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633352, "dur": 22, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633377, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633402, "dur": 25, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633430, "dur": 26, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633457, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633459, "dur": 17, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633479, "dur": 23, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633505, "dur": 27, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633536, "dur": 18, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633558, "dur": 20, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633585, "dur": 19, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633608, "dur": 135, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633745, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633889, "dur": 37, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633929, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560633931, "dur": 182, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634118, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634155, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634157, "dur": 24, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634185, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634187, "dur": 31, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634222, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634245, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634247, "dur": 134, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634384, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634412, "dur": 44, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634459, "dur": 20, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634482, "dur": 18, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634502, "dur": 20, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634524, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634543, "dur": 20, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634565, "dur": 18, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634587, "dur": 15, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634604, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634623, "dur": 94, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634720, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634749, "dur": 21, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634772, "dur": 16, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634793, "dur": 19, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634814, "dur": 86, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634902, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634929, "dur": 23, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634955, "dur": 19, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560634977, "dur": 83, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635062, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635087, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635090, "dur": 23, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635115, "dur": 19, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635137, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635154, "dur": 87, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635244, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635275, "dur": 23, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635300, "dur": 20, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635323, "dur": 17, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635342, "dur": 15, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635360, "dur": 20, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635382, "dur": 19, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635504, "dur": 1, "ph": "X", "name": "ProcessMessages 79", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635506, "dur": 51, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635560, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635563, "dur": 105, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635673, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635715, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635717, "dur": 34, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635754, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635756, "dur": 28, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635786, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635789, "dur": 129, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635922, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635954, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635955, "dur": 29, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635988, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560635990, "dur": 25, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636018, "dur": 206, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636227, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636259, "dur": 25, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636286, "dur": 16, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636305, "dur": 140, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636450, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636487, "dur": 95, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636584, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636611, "dur": 18, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636631, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636649, "dur": 13, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636665, "dur": 120, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636788, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636807, "dur": 14, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636824, "dur": 23, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636849, "dur": 14, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636865, "dur": 130, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560636999, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637102, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637104, "dur": 30, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637138, "dur": 104, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637245, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637273, "dur": 16, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637291, "dur": 17, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637311, "dur": 91, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637404, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637445, "dur": 20, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637469, "dur": 56, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637527, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637529, "dur": 17, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637550, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637649, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637672, "dur": 17, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637692, "dur": 23, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637718, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637719, "dur": 14, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637736, "dur": 82, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637820, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637836, "dur": 13, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637852, "dur": 18, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637873, "dur": 16, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637891, "dur": 81, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637975, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560637996, "dur": 17, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638016, "dur": 18, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638037, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638054, "dur": 20, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638076, "dur": 84, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638163, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638187, "dur": 21, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638210, "dur": 16, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638229, "dur": 13, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638244, "dur": 101, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638350, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638386, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638389, "dur": 31, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638423, "dur": 72, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638497, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638517, "dur": 16, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638535, "dur": 28, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638566, "dur": 78, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638646, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638668, "dur": 13, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638683, "dur": 16, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638702, "dur": 16, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638723, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638800, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638836, "dur": 26, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638865, "dur": 17, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638884, "dur": 81, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638967, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560638995, "dur": 27, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639025, "dur": 23, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639051, "dur": 96, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639152, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639205, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639208, "dur": 23, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639233, "dur": 108, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639345, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639371, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639395, "dur": 25, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639423, "dur": 86, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639513, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639534, "dur": 22, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639558, "dur": 19, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639580, "dur": 92, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639674, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639701, "dur": 17, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639720, "dur": 15, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639738, "dur": 80, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639822, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639846, "dur": 17, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639866, "dur": 16, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639884, "dur": 75, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639961, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560639982, "dur": 30, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640015, "dur": 15, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640034, "dur": 113, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640150, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640171, "dur": 17, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640190, "dur": 20, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640214, "dur": 17, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640234, "dur": 70, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640306, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640341, "dur": 35, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640378, "dur": 13, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640394, "dur": 74, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640471, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640493, "dur": 46, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640542, "dur": 102, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640647, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640670, "dur": 20, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640695, "dur": 14, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640711, "dur": 101, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640816, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640847, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640849, "dur": 26, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640878, "dur": 14, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640894, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560640976, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641001, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641003, "dur": 22, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641028, "dur": 82, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641113, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641140, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641141, "dur": 18, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641162, "dur": 20, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641186, "dur": 78, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641268, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641293, "dur": 18, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641313, "dur": 17, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641333, "dur": 78, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641413, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641437, "dur": 26, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641464, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641466, "dur": 18, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641487, "dur": 74, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641565, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641597, "dur": 19, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641618, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641620, "dur": 18, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641639, "dur": 15, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641658, "dur": 79, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641739, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641766, "dur": 28, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641797, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641799, "dur": 33, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641835, "dur": 92, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641931, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641959, "dur": 21, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560641983, "dur": 17, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642002, "dur": 79, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642083, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642102, "dur": 14, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642119, "dur": 18, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642139, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642140, "dur": 16, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642160, "dur": 73, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642236, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642259, "dur": 18, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642280, "dur": 16, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642300, "dur": 73, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642375, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642399, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642401, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642426, "dur": 28, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642456, "dur": 77, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642536, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642564, "dur": 20, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642587, "dur": 16, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642606, "dur": 76, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642685, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642709, "dur": 19, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642733, "dur": 18, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642754, "dur": 74, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642830, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642849, "dur": 15, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642868, "dur": 30, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642902, "dur": 13, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642918, "dur": 70, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560642990, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643015, "dur": 13, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643031, "dur": 36, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643070, "dur": 14, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643087, "dur": 74, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643163, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643187, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643210, "dur": 28, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643241, "dur": 18, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643261, "dur": 18, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643282, "dur": 21, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643306, "dur": 16, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643325, "dur": 13, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643341, "dur": 12, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643355, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643374, "dur": 72, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643448, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643473, "dur": 17, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643493, "dur": 19, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643515, "dur": 13, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643531, "dur": 74, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643607, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643609, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643632, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643634, "dur": 15, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643652, "dur": 15, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643669, "dur": 79, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643752, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643777, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643779, "dur": 21, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643802, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643804, "dur": 19, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643827, "dur": 17, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643846, "dur": 26, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643875, "dur": 16, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643894, "dur": 27, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643927, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560643947, "dur": 64, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644013, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644057, "dur": 22, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644082, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644104, "dur": 77, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644183, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644212, "dur": 25, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644241, "dur": 26, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644270, "dur": 27, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644301, "dur": 23, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644327, "dur": 22, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644351, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644367, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644386, "dur": 14, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644402, "dur": 87, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644492, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644520, "dur": 26, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644549, "dur": 16, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644567, "dur": 80, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644652, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644674, "dur": 17, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644693, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644715, "dur": 18, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644735, "dur": 20, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644758, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644780, "dur": 19, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644802, "dur": 15, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644819, "dur": 15, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644836, "dur": 71, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644910, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644930, "dur": 13, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644945, "dur": 17, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644964, "dur": 17, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560644985, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645009, "dur": 22, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645036, "dur": 19, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645058, "dur": 15, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645078, "dur": 17, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645098, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645118, "dur": 80, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645200, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645230, "dur": 21, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645254, "dur": 17, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645274, "dur": 21, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645300, "dur": 21, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645323, "dur": 15, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645343, "dur": 22, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645367, "dur": 13, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645382, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645402, "dur": 18, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645423, "dur": 66, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645491, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645508, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645510, "dur": 100, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645618, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560645649, "dur": 384, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646037, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646079, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646082, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646104, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646106, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646129, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646130, "dur": 15, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646149, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646171, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646199, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646236, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646238, "dur": 20, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646262, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646289, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646290, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646320, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646350, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646352, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646375, "dur": 22, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646401, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646428, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646431, "dur": 22, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646456, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646480, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646500, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646502, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646523, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646542, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646544, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646564, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646585, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646603, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646692, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646726, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646729, "dur": 27, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646759, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646762, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646797, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646826, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646854, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646856, "dur": 21, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646880, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646905, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646911, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646946, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646948, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646981, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560646983, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647011, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647013, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647046, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647048, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647074, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647076, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647106, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647108, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647138, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647140, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647165, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647166, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647199, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647201, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647233, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647236, "dur": 29, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647267, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647269, "dur": 20, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647293, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647326, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647328, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647357, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647359, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647390, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647392, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647424, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647426, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647475, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647477, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647507, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647509, "dur": 26, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647538, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647541, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647579, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647581, "dur": 23, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647606, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647608, "dur": 25, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647636, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647638, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647660, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647662, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647694, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647696, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647733, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647735, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647764, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647766, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647802, "dur": 29, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647835, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647885, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647916, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647918, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647954, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647956, "dur": 23, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560647983, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648014, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648016, "dur": 88, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648107, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648109, "dur": 48, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648159, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648162, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648187, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648189, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648222, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648224, "dur": 27, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648255, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648286, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648288, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648307, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648309, "dur": 26, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648337, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648339, "dur": 28, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648369, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648372, "dur": 26, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648400, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648402, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648433, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648435, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648462, "dur": 15, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648479, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648563, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648592, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648594, "dur": 27, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648624, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648626, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648657, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648680, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648682, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648717, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648719, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648744, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648746, "dur": 25, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648773, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648775, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648803, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648805, "dur": 30, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648837, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648839, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648872, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648875, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648900, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648925, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648927, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648985, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560648986, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649024, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649026, "dur": 22, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649050, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649052, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649084, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649086, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649111, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649160, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649162, "dur": 23, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649187, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649190, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649213, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649219, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649239, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649241, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649270, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649272, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649311, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649339, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649341, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649370, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649372, "dur": 22, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649397, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649398, "dur": 20, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649421, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649423, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649445, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649476, "dur": 26, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649505, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649507, "dur": 19, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649530, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649558, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649587, "dur": 207, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649799, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649822, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649842, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560649844, "dur": 4446, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560654301, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560654306, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560654361, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560654364, "dur": 1333, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560655703, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560655737, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560655741, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560655771, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560655774, "dur": 351, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560656130, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560656155, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560656157, "dur": 2074, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560658239, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560658243, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560658306, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560658309, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560658332, "dur": 230, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560658567, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560658593, "dur": 658, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659256, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659279, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659325, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659342, "dur": 278, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659624, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659651, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659679, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659682, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659707, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659731, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659733, "dur": 18, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659755, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659778, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659796, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659815, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659831, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659833, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659856, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560659875, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660042, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660060, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660062, "dur": 449, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660515, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660540, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660542, "dur": 69, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660614, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660632, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660635, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660672, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660698, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660725, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660727, "dur": 16, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660747, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660767, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560660789, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661013, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661039, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661041, "dur": 178, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661223, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661238, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661270, "dur": 327, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661601, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661630, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661633, "dur": 36, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661672, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661688, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661708, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661724, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661747, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661766, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661788, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661805, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661850, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661867, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661885, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661901, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661931, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560661952, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662083, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662103, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662148, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662166, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662167, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662201, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662294, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662312, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662349, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662363, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662365, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662393, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662409, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662436, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662453, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662457, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662486, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662502, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662604, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662626, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662628, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662651, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662792, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662810, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662842, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662862, "dur": 105, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662971, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560662990, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663012, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663030, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663050, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663069, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663107, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663124, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663141, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663173, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663187, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663221, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663240, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663258, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663296, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663324, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663326, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663347, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663397, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663419, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663420, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663446, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663468, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663497, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663519, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663521, "dur": 63, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663588, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663605, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663628, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663645, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663665, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663696, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663714, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663739, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663762, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663765, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663788, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663808, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663910, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663929, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663978, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560663981, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664004, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664026, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664046, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664063, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664141, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664166, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664229, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664246, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664290, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664308, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664401, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664426, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664447, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664449, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664473, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664492, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664519, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664539, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664682, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664705, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664708, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664733, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664751, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664771, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664797, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664881, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560664903, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665089, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665112, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665114, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665141, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665183, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665205, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665225, "dur": 15, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665244, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665267, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665302, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665323, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665342, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665358, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665377, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665395, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665397, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665422, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665443, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665461, "dur": 148, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665613, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665631, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665650, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665667, "dur": 23, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665693, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665695, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665718, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665806, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665828, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665868, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665885, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665913, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665943, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665963, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560665965, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560666066, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560666080, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560666105, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560666124, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560666143, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560666242, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560666269, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560666271, "dur": 69, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560666344, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560666364, "dur": 976, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667348, "dur": 90, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667441, "dur": 2, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667445, "dur": 25, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667475, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667477, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667556, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667559, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667605, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667642, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667668, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667699, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667721, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667849, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667873, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667917, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667935, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667937, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560667977, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668001, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668050, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668073, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668076, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668172, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668175, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668206, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668234, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668237, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668259, "dur": 339, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668604, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668648, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668650, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668755, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668776, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668780, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668805, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668853, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668878, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668881, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668939, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668943, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668974, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560668977, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669005, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669013, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669042, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669070, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669115, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669140, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669158, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669249, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669278, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669280, "dur": 425, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669708, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669710, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669740, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669799, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669831, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669881, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560669912, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560670032, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560670060, "dur": 637, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560670700, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560670703, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560670731, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560670755, "dur": 257, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560671014, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560671018, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560671050, "dur": 920, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560671976, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560672011, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560672013, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560672038, "dur": 247, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560672289, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560672319, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560672323, "dur": 416, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560672744, "dur": 662, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560673411, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560673444, "dur": 905, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560674353, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560674355, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560674389, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560674395, "dur": 58501, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560732914, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560732920, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560732969, "dur": 4335, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405560737322, "dur": 499629, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561236967, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561236975, "dur": 256, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561237233, "dur": 19, "ph": "X", "name": "ProcessMessages 4212", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561237253, "dur": 346641, "ph": "X", "name": "ReadAsync 4212", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561583904, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561583907, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561583942, "dur": 20, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561583963, "dur": 25511, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561609485, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561609489, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561609514, "dur": 153772, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561763296, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561763300, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561763333, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561763336, "dur": 177, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561763519, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561763548, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561763549, "dur": 111518, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561875081, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561875085, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561875120, "dur": 16, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561875137, "dur": 20134, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561895284, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561895287, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561895324, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561895326, "dur": 57024, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561952358, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561952362, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561952395, "dur": 21, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561952417, "dur": 19593, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561972022, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561972027, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561972105, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561972108, "dur": 1329, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561973443, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561973445, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561973480, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405561973508, "dur": 92145, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562065665, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562065669, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562065712, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562065715, "dur": 71954, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562137678, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562137682, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562137700, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562137703, "dur": 946, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562138655, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562138688, "dur": 21, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562138710, "dur": 466, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562139181, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562139201, "dur": 419, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 25108, "tid": 12884901888, "ts": 1752405562139622, "dur": 32492, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 25108, "tid": 11895, "ts": 1752405562188282, "dur": 3441, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 25108, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 25108, "tid": 8589934592, "ts": 1752405560602911, "dur": 655678, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 25108, "tid": 8589934592, "ts": 1752405561258592, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 25108, "tid": 8589934592, "ts": 1752405561258595, "dur": 1667, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 25108, "tid": 11895, "ts": 1752405562191726, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 25108, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 25108, "tid": 4294967296, "ts": 1752405560571661, "dur": 1601321, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752405560575500, "dur": 8792, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752405562173136, "dur": 4955, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752405562175478, "dur": 83, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 25108, "tid": 4294967296, "ts": 1752405562178243, "dur": 26, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 25108, "tid": 11895, "ts": 1752405562191738, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752405560601098, "dur": 70, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405560601207, "dur": 2671, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405560603894, "dur": 2376, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405560606420, "dur": 128, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752405560606548, "dur": 487, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405560607919, "dur": 1326, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_D30C54CC03ED4A77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405560610150, "dur": 2372, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2804DA6C2AC419DA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405560613106, "dur": 146, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_3AA533410EF50020.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405560613402, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752405560613594, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1752405560613810, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_0B080C2548A382FC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405560614379, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Services.Core.ref.dll_AC0C794516F68F7D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405560614502, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752405560614740, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1752405560615988, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752405560617377, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405560617775, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_207F21C8D3D24A17.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405560617953, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405560618770, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405560618954, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752405560621073, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405560622975, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405560626091, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1752405560626487, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405560626744, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1752405560626825, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405560627070, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405560632094, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13821562750565263257.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405560632296, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8070994851108164409.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1752405560634610, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Sirenix.OdinInspector.Modules.UnityMathematics.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1752405560607061, "dur": 39305, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405560646385, "dur": 1493175, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405562139561, "dur": 135, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405562139696, "dur": 56, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405562139911, "dur": 73, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405562140008, "dur": 24085, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1752405560607064, "dur": 39328, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560646415, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560646538, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_38C2F330433306EF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405560646952, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_9F5344B509F71D5B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405560647110, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_852A485882348CB3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405560647239, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9C9702B5BC184E4F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405560648417, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752405560648548, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTStub.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752405560648931, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560648989, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752405560649074, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752405560649229, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1752405560649348, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560649519, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752405560649729, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1752405560649893, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10746227388266335481.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752405560650009, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1930223300244423128.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752405560650138, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11728507804161959611.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1752405560650377, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560650688, "dur": 633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560651322, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560651978, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560652177, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560652367, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560652556, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560653025, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Util\\IRetryPolicy.cs"}}, {"pid": 12345, "tid": 1, "ts": 1752405560652824, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560653781, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560654224, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560654673, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560655095, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560655745, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560655957, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560656513, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560656730, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560656970, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560657163, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560657353, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560657649, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560657948, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560658001, "dur": 1112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560659113, "dur": 1139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560660253, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405560660582, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405560661202, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Purchasing.SecurityCore.ref.dll_E77B4FF6B2C2293F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405560661332, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityStub.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405560661558, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560661616, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityStub.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405560662172, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560662402, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560662467, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405560663165, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405560663354, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Telemetry.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405560663953, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560664369, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405560664574, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560664662, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405560665372, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560665616, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405560666263, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560666317, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405560666526, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1752405560667291, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560667440, "dur": 5490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560672933, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1752405560673143, "dur": 72188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560745341, "dur": 2549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.SecurityCore.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405560747891, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560747951, "dur": 2868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405560750820, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560750890, "dur": 2632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Telemetry.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405560753563, "dur": 11796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Analytics.DataPrivacy.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1752405560765795, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560765991, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560766132, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560766586, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560766780, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560767210, "dur": 1795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752405560769033, "dur": 1370526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560607065, "dur": 39338, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560646493, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_74CBFBDE1FABEA1C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560646831, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_549D4A511CBCEDDD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560646965, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_5FBF74718C353924.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560647150, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_5FBF74718C353924.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560647268, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_2EB828704772EA6F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560647965, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752405560648581, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752405560649109, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1752405560649225, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1752405560649311, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560649570, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405560649851, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17839364298002531413.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405560649982, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16584851037979262786.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1752405560650374, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560650595, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560651167, "dur": 610, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Metadata.dll"}}, {"pid": 12345, "tid": 2, "ts": 1752405560651020, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560652251, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560652457, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560653026, "dur": 758, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.services.core\\Editor\\Core\\Environments\\EnvironmentNotFoundException.cs"}}, {"pid": 12345, "tid": 2, "ts": 1752405560653015, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560654033, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560654602, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560655043, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560655680, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560655881, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560656066, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560656277, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560656455, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560656843, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560657127, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560657710, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560658420, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560659122, "dur": 1145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560660269, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560660468, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560660613, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560661477, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560661588, "dur": 543, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560662143, "dur": 600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560662744, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560663070, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560663263, "dur": 809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560664073, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560664225, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560664381, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560664702, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560665002, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560665865, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560666178, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560666288, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560666479, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560666605, "dur": 1039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560667679, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560667941, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560668318, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560668466, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560668565, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560669558, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560669720, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560669961, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560670708, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560670781, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560670897, "dur": 2040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560672947, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1752405560673157, "dur": 74993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560748152, "dur": 4505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560752714, "dur": 3356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560756114, "dur": 2247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.DevX.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560758406, "dur": 12520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1752405560770928, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752405560771084, "dur": 1368502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560607067, "dur": 39342, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560646496, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A430C75F08F988D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560646990, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_A430C75F08F988D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560647146, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_8443955A624F5BB6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560647277, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_6C968D1B8DB525D3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560647463, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560647973, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405560648142, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405560648319, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752405560648541, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405560648668, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405560648937, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405560649063, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405560649227, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1752405560649315, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560649448, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405560649607, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1752405560649929, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12176746820506266677.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405560650151, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15895841462048577653.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405560650255, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5138823106612435834.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1752405560650419, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560650657, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560651833, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560652058, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560652286, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560652480, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560652735, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560653026, "dur": 757, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\AAR\\Models\\GoogleRetrieveProductsFailureReason.cs"}}, {"pid": 12345, "tid": 3, "ts": 1752405560652963, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560653954, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560654607, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560655227, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560655702, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560655915, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560656092, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560656316, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560656518, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560656789, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560657080, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560657302, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560657519, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560658044, "dur": 1066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560659143, "dur": 1270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560660415, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560660686, "dur": 1624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560662348, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560662618, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560662884, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560662952, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560663160, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560663713, "dur": 746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560664459, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560664609, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560664883, "dur": 1408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560666292, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560666581, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560666804, "dur": 1566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560668372, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560668528, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560668716, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560668911, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560669449, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560669622, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560669805, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1752405560669868, "dur": 726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.Purchasing.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560670595, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560670672, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560670743, "dur": 2231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560672974, "dur": 72377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560745361, "dur": 2567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560747931, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560747992, "dur": 2670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560750696, "dur": 3185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Configuration.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560753882, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560754058, "dur": 3323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560757382, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560757533, "dur": 8781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.Purchasing.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1752405560766599, "dur": 2642, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752405560769246, "dur": 1370353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560607100, "dur": 39322, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560646429, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5160C8205B154597.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405560646714, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560646812, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560646954, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_225817FABB090C56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405560647144, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_E88B09B6A6B366F7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405560647290, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_D37EEA1667D204B7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405560647971, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560648135, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560648303, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTStub.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752405560648428, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560648625, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560648841, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560648996, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560649075, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Codeless.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560649228, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1752405560649324, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560649453, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560649527, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEditor.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560649668, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560649832, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12381143614081962271.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560649917, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560649976, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11148342699003047676.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560650196, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14443923926707600816.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1752405560650251, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560650358, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560650582, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560650809, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560651598, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560651855, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560652081, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560652271, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560652568, "dur": 1616, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.cursor\\Editor\\Messaging\\ExceptionEventArgs.cs"}}, {"pid": 12345, "tid": 4, "ts": 1752405560652472, "dur": 1817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560654289, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560654913, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560655280, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560655727, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560656200, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560656394, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560656664, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560657032, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560657236, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560657472, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560657684, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560658346, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560659124, "dur": 1141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560660267, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405560660567, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560660650, "dur": 3028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405560663678, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560664167, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405560664497, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405560665502, "dur": 1484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560667073, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560667128, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/IAPResolver.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1752405560667378, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/IAPResolver.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1752405560667928, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560668060, "dur": 4910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560672970, "dur": 72392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560745373, "dur": 2523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Registration.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405560747946, "dur": 4759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405560752707, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560752983, "dur": 2463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405560755447, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560755628, "dur": 2067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405560757701, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560757770, "dur": 2065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405560759890, "dur": 2355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405560762278, "dur": 2121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405560764400, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752405560764466, "dur": 3304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1752405560767834, "dur": 1371727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560607090, "dur": 39325, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560646485, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_76BE4E04C75E42FB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405560646803, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560646987, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_ED1D4BDE456EA07B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405560647099, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_ED1D4BDE456EA07B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405560647255, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_8C8256DD42673B8D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405560648223, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1752405560648426, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405560648724, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1752405560648921, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560649005, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752405560649111, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1752405560649283, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560649607, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1752405560649915, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9254761643517088472.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405560649993, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1687125193959377702.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405560650071, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560650153, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1687125193959377702.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405560650350, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16391513446862009207.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1752405560650424, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560650640, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560651667, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560651944, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560652483, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560652693, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560653029, "dur": 873, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\StoreCallbackExtensionMethods.cs"}}, {"pid": 12345, "tid": 5, "ts": 1752405560652912, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560653996, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560654638, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560655421, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560655677, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560655856, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560656034, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560656272, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560656546, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560656804, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560657148, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560657348, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560657587, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560658217, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560659119, "dur": 1154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560660274, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405560660573, "dur": 1235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405560661809, "dur": 533, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560662393, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405560662600, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560662719, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405560663404, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560663474, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405560663659, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405560663838, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405560664745, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560665101, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560665158, "dur": 1521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1752405560666680, "dur": 569, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560667300, "dur": 5633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560672941, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1752405560673168, "dur": 72259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560745434, "dur": 2433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405560747868, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560747978, "dur": 2560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405560750539, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560750663, "dur": 2848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405560753512, "dur": 1380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560754911, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405560757384, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560757617, "dur": 2271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405560759892, "dur": 1395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560761294, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405560763522, "dur": 2407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.XR.LegacyInputHelpers.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1752405560766052, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560766295, "dur": 529, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560766942, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405560767396, "dur": 1128506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752405561895935, "dur": 170504, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752405561895903, "dur": 170539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1752405562066488, "dur": 73076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560607124, "dur": 39305, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560646436, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_4CE2C9A0BAA62A6D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560646787, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_250F5FCDDF66F245.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560647148, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_FCFF39C391BCB29A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560647229, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DF2CF8DDD7D6F306.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560647863, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560648049, "dur": 7009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405560655227, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560655876, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560656070, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560656274, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560656514, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560656940, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560657454, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560657811, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560658117, "dur": 1008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560659125, "dur": 1352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560660478, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560660720, "dur": 1502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405560662223, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560662289, "dur": 1564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405560663854, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560664054, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560664292, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560664654, "dur": 1374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405560666028, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560666179, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560666269, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560666481, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405560667301, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560667374, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560667609, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560667738, "dur": 949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405560668688, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560668851, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560668972, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560669034, "dur": 2366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405560671403, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560671617, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560671819, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405560672701, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560672817, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560672943, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1752405560673153, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405560673549, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405560674035, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405560675031, "dur": 95, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405560676645, "dur": 908092, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405561590702, "dur": 19368, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752405561590368, "dur": 19815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1752405561610185, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405561610846, "dur": 150442, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1752405561610263, "dur": 152100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405561763610, "dur": 243, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752405561764684, "dur": 111235, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1752405561895906, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752405561895896, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1752405561896098, "dur": 243470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560607149, "dur": 39289, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560646445, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_0AA979E9F85FAA0B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405560646789, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0FEF1F0834F797BE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405560646860, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560646950, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_67755EAB3D0FEC1C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405560647195, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_213E7CCFE95EA0A1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405560647866, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405560648040, "dur": 8473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560656639, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405560656941, "dur": 2077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560659124, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560659212, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405560659427, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560660261, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405560660647, "dur": 1361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560662008, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560662435, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560662764, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405560663016, "dur": 920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560663937, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560664039, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1752405560664382, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560665425, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560665555, "dur": 1083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1752405560666670, "dur": 252, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560667644, "dur": 66098, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 7, "ts": 1752405560745323, "dur": 2589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.Internal.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560747959, "dur": 2551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleStub.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560750549, "dur": 2391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560752941, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560753001, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.Codeless.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560755390, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560755829, "dur": 2088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560757918, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560758276, "dur": 2167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560760445, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560760556, "dur": 1932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560762528, "dur": 2011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560764540, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405560764600, "dur": 2725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/IAPResolver.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405560767385, "dur": 823013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405561590421, "dur": 170866, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752405561590401, "dur": 172504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405561764113, "dur": 228, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752405561764806, "dur": 188396, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/2000b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1752405561972571, "dur": 165917, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752405561972562, "dur": 165928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1752405562138507, "dur": 1012, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1752405560607175, "dur": 39272, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560646453, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D7C1E683AFA161F2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405560646816, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_A4F012C5A38D9152.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405560647180, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_1947EF48CF5DAD26.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405560647375, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_FBDCCF39A60FD123.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405560647456, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560648053, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Purchasing.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405560648132, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1752405560648366, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405560648535, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Internal.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405560648607, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405560648675, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405560648986, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405560649074, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752405560649194, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Analytics.DataPrivacy.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752405560649287, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560649732, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/IAPResolver.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1752405560649992, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17546307333235763125.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405560650092, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6832337476128879279.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1752405560650876, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560651985, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560652201, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560652567, "dur": 1391, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite\\Editor\\SpriteEditor\\SpriteNameFileIdPair.cs"}}, {"pid": 12345, "tid": 8, "ts": 1752405560652480, "dur": 1608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560654088, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560654499, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560655248, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560655920, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560656101, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560656319, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560656613, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560656840, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560657621, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560658024, "dur": 1087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560659111, "dur": 1139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560660252, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405560660465, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560660576, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405560661279, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560661514, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405560661740, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560661826, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405560662549, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560662806, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405560663019, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560663510, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405560664245, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560664366, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405560664677, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560664776, "dur": 1149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405560665970, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560666271, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1752405560666528, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1752405560667298, "dur": 5662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560672961, "dur": 72473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560745440, "dur": 2381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Threading.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405560747823, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560748024, "dur": 2592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405560750660, "dur": 4530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Scheduler.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405560755192, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560755348, "dur": 11729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1752405560767157, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752405560767841, "dur": 1371714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560607193, "dur": 39262, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560646461, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_12BB074DEDA731F9.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560646982, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_F2E90249D91087BE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560647033, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560647097, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_F2E90249D91087BE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560647203, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_18EDBD7A6FB6E440.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560647465, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560647829, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405560647993, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.SecurityCore.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1752405560648218, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560648287, "dur": 174, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1752405560648463, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405560648532, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405560648719, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1752405560648863, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405560649112, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1752405560649229, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405560649309, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560649604, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405560649930, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014880287252381842.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405560649996, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014880287252381842.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1752405560650088, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560650381, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560650581, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560650805, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560651521, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560652536, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560652772, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560653024, "dur": 762, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\AAR\\GooglePlayStoreService.cs"}}, {"pid": 12345, "tid": 9, "ts": 1752405560653005, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560654015, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560654501, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560655090, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560655732, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560655959, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560656191, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560656457, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560656742, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560656945, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560657147, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560657368, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560657801, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560658341, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560659126, "dur": 1134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560660262, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560660495, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560660584, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405560661318, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560661550, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560661864, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560662093, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560662345, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405560663030, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560663217, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405560663822, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560663974, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560664206, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405560664978, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560665313, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560665748, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560665953, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Analytics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1752405560666264, "dur": 1267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1752405560667576, "dur": 5365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560672942, "dur": 75073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560748023, "dur": 2805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Purchasing.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405560750830, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560750972, "dur": 3206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleMacosStub.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405560754181, "dur": 665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560754853, "dur": 5383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1752405560760238, "dur": 4771, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560765138, "dur": 1524, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Purchasing.Common.dll"}}, {"pid": 12345, "tid": 9, "ts": 1752405560766663, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1752405560766944, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560767243, "dur": 3814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752405560771105, "dur": 1368487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560607217, "dur": 39244, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560646467, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_87D80D01DE8F4B44.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560646808, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_9A8288CF5DD2C0CE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560646917, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_061CFA2463039477.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560647157, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560647212, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_96FCD8B7D5267888.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560647291, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_C7CE81B115F5925C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560648071, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752405560648248, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1752405560648428, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405560648683, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1752405560648943, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752405560649068, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1752405560649259, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405560649355, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560649573, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1752405560649685, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405560649896, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4769955788402727329.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405560650022, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17392071123951179896.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405560650256, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13245841461569978743.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405560650408, "dur": 286, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7033852615358263443.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1752405560650695, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560651688, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560651918, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560652129, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560652414, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560652680, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560653031, "dur": 974, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\Interfaces\\IGooglePlayConfigurationInternal.cs"}}, {"pid": 12345, "tid": 10, "ts": 1752405560652960, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560654160, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560654799, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560655415, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560655714, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560655924, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560656125, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560656323, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560656527, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560656786, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560656975, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560657187, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560657403, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560657634, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560657914, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560658012, "dur": 1100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560659112, "dur": 1142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560660255, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleCore.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560660486, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560660839, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560661184, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560661700, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560661793, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560662024, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560662133, "dur": 657, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560662792, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560663019, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405560663484, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560663930, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Networking.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1752405560664268, "dur": 1003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Networking.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405560665272, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560665644, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405560666566, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560666632, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560666744, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560667288, "dur": 1242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560668532, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1752405560668971, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560669039, "dur": 3904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560672944, "dur": 75061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560748015, "dur": 2758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405560750818, "dur": 3125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Networking.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405560753945, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560754052, "dur": 2412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405560756506, "dur": 2144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Configuration.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405560758705, "dur": 2053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405560760797, "dur": 1958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Windsurf.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405560762798, "dur": 2064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1752405560764863, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752405560765115, "dur": 1534, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Services.Core.Scheduler.dll"}}, {"pid": 12345, "tid": 10, "ts": 1752405560766651, "dur": 2382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.Advertisements.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1752405560769051, "dur": 1370553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560607236, "dur": 39233, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560646476, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CFC043736E130462.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405560646814, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560646974, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_7B4876BD556A0B66.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405560647231, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_D18D531254CCDD3B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405560647400, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560647970, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1752405560648092, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1752405560648232, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1752405560648416, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405560648495, "dur": 157, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405560648685, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405560649141, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Windsurf.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1752405560649296, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560649604, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405560649687, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405560649821, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3037372816226278216.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405560649983, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6054964502542534126.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405560650405, "dur": 259, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10885039498462050167.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1752405560650665, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560651351, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560652019, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560652242, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560652567, "dur": 1399, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Editor\\Analytics\\DebugAnalytics.cs"}}, {"pid": 12345, "tid": 11, "ts": 1752405560652441, "dur": 1621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560654062, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560655446, "dur": 701, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Runtime\\2D\\Shadows\\ShadowProvider\\Providers\\ShadowShapeProvider2D_Utility.cs"}}, {"pid": 12345, "tid": 11, "ts": 1752405560655341, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560656285, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560656768, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560657055, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560657274, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560657474, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560658014, "dur": 1114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560659129, "dur": 1250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560660382, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405560660758, "dur": 936, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560661700, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405560662291, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560662469, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405560663121, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560663308, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405560663476, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405560664100, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560664322, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1752405560664840, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405560665528, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560665640, "dur": 1690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1752405560667333, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560667438, "dur": 5525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560672964, "dur": 72445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560745417, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405560747985, "dur": 3028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405560751015, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752405560751214, "dur": 4452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405560755708, "dur": 13197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1752405560769023, "dur": 1370588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560607261, "dur": 39269, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560646531, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1D9B8A6D435426D1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405560646860, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560647258, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_BBA2C8071FCB7521.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405560647968, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_CE65D8670DA73F5C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405560648291, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1752405560648548, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleMacosStub.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405560648670, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.Stores.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405560648904, "dur": 367, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405560649283, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560649986, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5498571500906958659.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405560650079, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5498571500906958659.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405560650221, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5814885297447240720.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1752405560650379, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560650609, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560651603, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560651870, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560652093, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560652307, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560652564, "dur": 1536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Editor\\ShaderGraph\\UniversalProperties.cs"}}, {"pid": 12345, "tid": 12, "ts": 1752405560652540, "dur": 1917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560654458, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560655299, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560655743, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560655950, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560656152, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560656380, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560656613, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560656855, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560657166, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560657376, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560657700, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560658269, "dur": 851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560659121, "dur": 1136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560660259, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405560660511, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560661366, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405560661588, "dur": 1809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560663398, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560663890, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Threading.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560665026, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560665294, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560665356, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405560665601, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560666235, "dur": 695, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560666972, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405560667221, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560668221, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560668306, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560668419, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405560668959, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560669082, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560669839, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560669964, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1752405560670113, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Analytics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560670565, "dur": 2414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560672980, "dur": 72361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560745350, "dur": 2625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560748027, "dur": 2623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Components.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560750650, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560751066, "dur": 2482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.WinRTCore.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560753581, "dur": 2584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560756166, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560756250, "dur": 2217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560758523, "dur": 3483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560762008, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560762073, "dur": 2011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Analytics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560764117, "dur": 2200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1752405560766318, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560766597, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560766778, "dur": 489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405560767269, "dur": 497644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752405561264915, "dur": 874659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560607280, "dur": 39242, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560646523, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_96B4457290D314C3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405560647242, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560647975, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405560648240, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405560648348, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405560648414, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Scheduler.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405560648622, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Components.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405560648725, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1752405560648929, "dur": 349, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1752405560649291, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560649439, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405560649567, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Analytics.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1752405560649694, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1752405560649971, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17493449851061911080.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405560650041, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560650236, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15569745926999849003.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1752405560650419, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560650702, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560652076, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\TrackResourceCache.cs"}}, {"pid": 12345, "tid": 13, "ts": 1752405560652076, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560652782, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560653020, "dur": 749, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.services.core\\Editor\\Core\\Environments\\Settings\\EnvironmentIdentifier.cs"}}, {"pid": 12345, "tid": 13, "ts": 1752405560653007, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560654005, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560654477, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560655566, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560655759, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560656190, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560656609, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560656844, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560657057, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560657254, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560657436, "dur": 120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560657557, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560657773, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560658429, "dur": 706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560659135, "dur": 1336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560660472, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405560660908, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560661608, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405560661895, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405560662081, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Analytics.DataPrivacy.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405560662249, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Analytics.DataPrivacy.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560662710, "dur": 759, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560663484, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Internal.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405560663724, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Environments.Internal.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560664468, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560664534, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560664639, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405560664913, "dur": 1026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.AppleStub.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560666097, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560666528, "dur": 585, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560667123, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1752405560667280, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560667356, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560667840, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560667924, "dur": 5021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560672947, "dur": 72372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560745330, "dur": 2467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Environments.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560747798, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560747876, "dur": 2546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560750423, "dur": 478, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560750908, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560753427, "dur": 2478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560755951, "dur": 1972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560757960, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560760087, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560760149, "dur": 7003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1752405560767153, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405560767259, "dur": 493852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405561263351, "dur": 345, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 13, "ts": 1752405561263697, "dur": 1127, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 13, "ts": 1752405561264825, "dur": 78, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 13, "ts": 1752405561261116, "dur": 3792, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1752405561264908, "dur": 874672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560607299, "dur": 39201, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560646501, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_597300EC37484E29.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405560646914, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_E78A653116C8BD22.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405560647063, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560647241, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560647367, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560647504, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_4B8C89D368746A27.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405560647815, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405560648027, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1752405560648317, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1752405560648473, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Device.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405560648554, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1752405560648687, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1752405560648987, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405560649066, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.XR.LegacyInputHelpers.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405560649194, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1752405560649298, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560649719, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/IAPResolver.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1752405560649977, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9365680292007807955.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1752405560650963, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560651618, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560651847, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560652056, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560652277, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560652484, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560652740, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560653030, "dur": 776, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\Android\\GooglePlay\\AAR\\Interfaces\\IGoogleQueryPurchasesService.cs"}}, {"pid": 12345, "tid": 14, "ts": 1752405560652964, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560653984, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560654540, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Drawing\\Blackboard\\BlackboardInputInfo.cs"}}, {"pid": 12345, "tid": 14, "ts": 1752405560654475, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560655359, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560655613, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560655814, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560656005, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560656490, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560656913, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560657392, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560657654, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560658118, "dur": 1000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560659118, "dur": 1131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560660250, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405560660452, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560660516, "dur": 1699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405560662216, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560662353, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405560662577, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405560663189, "dur": 1142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560664399, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560664526, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405560664833, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560664895, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405560665627, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560665997, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405560666961, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560667297, "dur": 2487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560669786, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1752405560669994, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.XR.LegacyInputHelpers.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1752405560670507, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560670593, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560670669, "dur": 2288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560672958, "dur": 72460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560745427, "dur": 2408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405560747884, "dur": 2537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.SecurityStub.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405560750423, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560750504, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405560752825, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560752962, "dur": 2715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405560755683, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560755744, "dur": 5003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1752405560760749, "dur": 4318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560765105, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.SpatialTracking.dll"}}, {"pid": 12345, "tid": 14, "ts": 1752405560765419, "dur": 604, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560766206, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560766596, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560766895, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560767001, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405560767404, "dur": 1205162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1752405561972596, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\2000b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1752405561972568, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1752405561972854, "dur": 1475, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1752405561974338, "dur": 165224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560607328, "dur": 39168, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560646497, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WebGLModule.dll_7671FA4B6C66E14A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405560647236, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560648183, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1752405560648322, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405560648626, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Postprocessing.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1752405560648810, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Registration.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405560649064, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1752405560649141, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1752405560649290, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560649484, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405560649630, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405560649728, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1752405560649820, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3755261912168333632.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405560649887, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3755261912168333632.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405560649961, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4763750419435726923.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405560650091, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560650238, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14919082851048885018.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1752405560650403, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560650758, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 15, "ts": 1752405560650628, "dur": 1553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560652182, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560652389, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560652664, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560653022, "dur": 751, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\FakeStore\\UIFakeStoreDropdown.cs"}}, {"pid": 12345, "tid": 15, "ts": 1752405560652878, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560653837, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560654082, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560654990, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560655994, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560656203, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560656382, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560656600, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560657001, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560657212, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560657602, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560657887, "dur": 76, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560658030, "dur": 1078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560659146, "dur": 1109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560660266, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405560660558, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560660717, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTCore.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560661561, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560661653, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTStub.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405560661856, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Purchasing.WinRTStub.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560662406, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560662718, "dur": 1439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Analytics.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560664158, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560664319, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEditor.SpatialTracking.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405560664642, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEditor.SpatialTracking.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560665405, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560666093, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560666785, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560667048, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560667103, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405560667277, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560667485, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560668304, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405560668462, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560668521, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1752405560668651, "dur": 831, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560669490, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/UnityEngine.Advertisements.DevX.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560669945, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560670017, "dur": 2932, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560672955, "dur": 72629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560745592, "dur": 2329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560747922, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560748119, "dur": 2583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.Stores.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560750738, "dur": 2589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Analytics.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560753329, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560753813, "dur": 2618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.WinRTStub.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560756432, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560756563, "dur": 2003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560758567, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1752405560758747, "dur": 2216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEditor.SpatialTracking.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560761000, "dur": 1835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560762877, "dur": 1902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Advertisements.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560764823, "dur": 3569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1752405560768464, "dur": 1371092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560607357, "dur": 39135, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560646493, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_0EE7DB51FB8BE602.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405560647205, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560647312, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C2741807B1FC8886.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405560648186, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Configuration.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1752405560648423, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405560648547, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405560648682, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/UnityEngine.SpatialTracking.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405560648841, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Services.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1752405560649172, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1752405560649306, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560649447, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Unity.Analytics.DataPrivacy.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405560649556, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/2000b0aE.dag/Analytics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1752405560649923, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3118966511047783685.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405560650008, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2359241780799432324.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405560650130, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17348034628316609254.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1752405560650401, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560650615, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560651339, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.34f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Hosting.Abstractions.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752405560651301, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560653029, "dur": 760, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.purchasing\\Runtime\\Stores\\AppleAppStore\\GetAppleProductMetadataExtension.cs"}}, {"pid": 12345, "tid": 16, "ts": 1752405560652905, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560653889, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560654381, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560655072, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560655523, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560655708, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560656369, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560656597, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560657209, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560657466, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560658071, "dur": 1046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560659117, "dur": 1146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560660274, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405560660522, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560660587, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405560661313, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405560661553, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405560661823, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405560661991, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560662411, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405560663022, "dur": 1061, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560664090, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405560664387, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560664455, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405560665239, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560665389, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405560666272, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405560666520, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405560667196, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560667294, "dur": 1563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560668859, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1752405560669024, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560669095, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/2000b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1752405560669652, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560669745, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560669821, "dur": 3147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560672968, "dur": 72431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560745408, "dur": 2437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.Purchasing.AppleCore.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405560747889, "dur": 2194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405560750122, "dur": 2407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405560752530, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560752770, "dur": 7961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/2000b0aE.dag/post-processed/Unity.Services.Core.Device.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1752405560760733, "dur": 4327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560765118, "dur": 1538, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Services.Core.Device.dll"}}, {"pid": 12345, "tid": 16, "ts": 1752405560766658, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1752405560766943, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560767192, "dur": 1255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1752405560768473, "dur": 1371085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752405562170810, "dur": 1775, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 25108, "tid": 11895, "ts": 1752405562192362, "dur": 12005, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 25108, "tid": 11895, "ts": 1752405562204398, "dur": 2034, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 25108, "tid": 11895, "ts": 1752405562185485, "dur": 21728, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}