using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer), typeof(MeshCollider))]
public class IslandGenerator : MonoBehaviour
{
    [Header("Island Settings")]
    [Tooltip("Overall size of the island in world units")]
    public float size = 50f;
    [Tooltip("Height of the island surface")]
    public float surfaceHeight = 0f;
    [Tooltip("Depth of the cliff edges")]
    public float edgeDepth = 5f;

    [Header("Shape Settings")]
    [Range(0f, 1f)]
    [Tooltip("How jagged the coastline is (0 = smooth circle, 1 = very jagged)")]
    public float coastVariation = 0.5f;
    [Tooltip("Random seed for island shape generation")]
    public int seed = 0;

    [Header("Checkerboard Settings")]
    [Tooltip("Size of each checkerboard tile")]
    public float tileSize = 1f;

    [Header("Appearance")]
    public Color grassColorLight = new Color(0.45f, 0.76f, 0.34f);
    public Color grassColorDark = new Color(0.41f, 0.69f, 0.31f);
    public Color cliffColor = new Color(0.36f, 0.25f, 0.2f);



    private MeshFilter meshFilter;
    private MeshCollider meshCollider;
    private MeshRenderer meshRenderer;



    public void GenerateIsland()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshCollider = GetComponent<MeshCollider>();
        meshRenderer = GetComponent<MeshRenderer>();

        Mesh mesh = new Mesh();
        mesh.name = "ProceduralIsland";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Color> colors = new List<Color>();
        List<Vector3> normals = new List<Vector3>();

        // Generate island as discrete tile pieces that form organic shape
        GenerateOrganicTileIsland(vertices, triangles, colors, normals);

        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.colors = colors.ToArray();
        mesh.normals = normals.ToArray();
        mesh.RecalculateBounds();

        meshFilter.sharedMesh = mesh;
        if (meshCollider != null) meshCollider.sharedMesh = mesh;

        // material if not already
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Particles/Unlit"));
        mat.SetColor("_BaseColor", Color.white);
        if (mat.HasProperty("_Cull")) mat.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        if (mat.HasProperty("_CullMode")) mat.SetInt("_CullMode", (int)UnityEngine.Rendering.CullMode.Off);
        meshRenderer.sharedMaterial = mat;
    }

    void GenerateOrganicTileIsland(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals)
    {
        // Use the Size parameter to determine island size in tiles
        int islandSizeInTiles = Mathf.Max(10, Mathf.RoundToInt(size / tileSize));

        // Dynamic padding based on island size - smaller padding for huge islands
        int padding = Mathf.Min(40, Mathf.Max(10, islandSizeInTiles / 10));
        int gridSize = islandSizeInTiles + padding;

        // No arbitrary size limit - let the user choose what they want!

        Debug.Log($"Island Generation: Size={size}, TileSize={tileSize}, IslandTiles={islandSizeInTiles}, GridSize={gridSize}, Padding={padding}");

        float startX = -gridSize * tileSize * 0.5f;
        float startZ = -gridSize * tileSize * 0.5f;

        // Create a 2D grid to track which tiles should exist
        bool[,] tileGrid = new bool[gridSize, gridSize];

        // Create island shape
        CreateIslandShape(tileGrid, gridSize, islandSizeInTiles);

        // Ensure connectivity: remove isolated pieces and fill holes
        EnsureConnectivity(tileGrid, gridSize);

        Debug.Log($"Generated island with grid size {gridSize}x{gridSize}, island size {islandSizeInTiles} tiles");

        // Generate actual 3D tiles from the grid (no gaps!)
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                if (tileGrid[x, z])
                {
                    float tileX = startX + x * tileSize;
                    float tileZ = startZ + z * tileSize;

                    // Checkerboard pattern
                    bool isLight = ((x + z) & 1) == 0;
                    Color tileColor = isLight ? grassColorLight : grassColorDark;

                    // Create seamless tile piece (no bevels)
                    CreateSeamlessTile(vertices, triangles, colors, normals, tileX, tileZ, tileSize, surfaceHeight, tileColor);
                }
            }
        }
    }

    void CreateIslandShape(bool[,] tileGrid, int gridSize, int islandSizeInTiles)
    {
        int centerX = gridSize / 2;
        int centerZ = gridSize / 2;
        float baseRadius = islandSizeInTiles * 0.5f; // Base island radius in tiles

        // Step 1: Create a solid circular base - this ensures we always have a complete island
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                int dx = x - centerX;
                int dz = z - centerZ;
                float distance = Mathf.Sqrt(dx * dx + dz * dz);

                // Create solid circle first
                if (distance <= baseRadius * 0.8f) // Inner 80% is always solid
                {
                    tileGrid[x, z] = true;
                }
                // Edge zone - add organic variation
                else if (distance <= baseRadius)
                {
                    // Use noise to vary the edge, but bias toward keeping tiles
                    float noiseValue = Mathf.PerlinNoise(x * 0.15f, z * 0.15f);

                    // How far into the edge zone are we? (0 = inner edge, 1 = outer edge)
                    float edgeProgress = (distance - baseRadius * 0.8f) / (baseRadius * 0.2f);

                    // Base probability of keeping the tile (higher near inner edge)
                    float keepProbability = 0.9f - edgeProgress * 0.4f; // 90% to 50%

                    // Apply coast variation
                    float variation = (noiseValue - 0.5f) * coastVariation * 0.5f;
                    float finalProbability = keepProbability + variation;

                    if (noiseValue > (1.0f - finalProbability))
                    {
                        tileGrid[x, z] = true;
                    }
                }
            }
        }
    }
    void FillInteriorHoles(bool[,] tileGrid, int gridSize)
    {
        // Fill holes that are completely surrounded by land
        for (int x = 1; x < gridSize - 1; x++)
        {
            for (int z = 1; z < gridSize - 1; z++)
            {
                if (!tileGrid[x, z]) // If this is a hole
                {
                    // Check if all 8 neighbors are land
                    bool allNeighborsAreLand = true;
                    for (int dx = -1; dx <= 1; dx++)
                    {
                        for (int dz = -1; dz <= 1; dz++)
                        {
                            if (dx == 0 && dz == 0) continue; // Skip center
                            if (!tileGrid[x + dx, z + dz])
                            {
                                allNeighborsAreLand = false;
                                break;
                            }
                        }
                        if (!allNeighborsAreLand) break;
                    }

                    // If completely surrounded, fill the hole
                    if (allNeighborsAreLand)
                    {
                        tileGrid[x, z] = true;
                    }
                }
            }
        }
    }

    void EnsureConnectivity(bool[,] tileGrid, int gridSize)
    {
        // Step 1: Find the largest connected component (main island)
        bool[,] visited = new bool[gridSize, gridSize];
        int largestComponentSize = 0;
        bool[,] largestComponent = new bool[gridSize, gridSize];

        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                if (tileGrid[x, z] && !visited[x, z])
                {
                    // Found a new component, measure its size
                    bool[,] currentComponent = new bool[gridSize, gridSize];
                    int componentSize = FloodFillComponent(tileGrid, visited, currentComponent, x, z, gridSize);

                    if (componentSize > largestComponentSize)
                    {
                        largestComponentSize = componentSize;
                        largestComponent = currentComponent;
                    }
                }
            }
        }

        // Step 2: Keep only the largest component (removes isolated islands)
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                tileGrid[x, z] = largestComponent[x, z];
            }
        }

        // Step 3: Fill interior holes (surrounded empty spaces)
        FillInteriorHoles(tileGrid, gridSize);
    }

    int FloodFillComponent(bool[,] tileGrid, bool[,] visited, bool[,] component, int startX, int startZ, int gridSize)
    {
        Stack<(int, int)> stack = new Stack<(int, int)>();
        stack.Push((startX, startZ));
        int componentSize = 0;

        while (stack.Count > 0)
        {
            (int x, int z) = stack.Pop();

            if (x < 0 || x >= gridSize || z < 0 || z >= gridSize || visited[x, z] || !tileGrid[x, z])
                continue;

            visited[x, z] = true;
            component[x, z] = true;
            componentSize++;

            // Add 4-connected neighbors
            stack.Push((x + 1, z));
            stack.Push((x - 1, z));
            stack.Push((x, z + 1));
            stack.Push((x, z - 1));
        }

        return componentSize;
    }





    void CreateSeamlessTile(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals,
                           float x, float z, float size, float height, Color color)
    {
        int baseIndex = vertices.Count;

        // Create perfectly aligned tiles with no gaps or bevels
        // Top face vertices
        vertices.Add(new Vector3(x, height, z));                    // 0
        vertices.Add(new Vector3(x + size, height, z));             // 1
        vertices.Add(new Vector3(x + size, height, z + size));      // 2
        vertices.Add(new Vector3(x, height, z + size));             // 3

        // Bottom face vertices
        vertices.Add(new Vector3(x, height - edgeDepth, z));                    // 4
        vertices.Add(new Vector3(x + size, height - edgeDepth, z));             // 5
        vertices.Add(new Vector3(x + size, height - edgeDepth, z + size));      // 6
        vertices.Add(new Vector3(x, height - edgeDepth, z + size));             // 7

        // Add colors for all vertices
        for (int i = 0; i < 8; i++)
        {
            colors.Add(i < 4 ? color : cliffColor); // Top face gets tile color, bottom gets cliff color
        }

        // Add normals
        for (int i = 0; i < 4; i++) normals.Add(Vector3.up);    // Top face
        for (int i = 0; i < 4; i++) normals.Add(Vector3.down);  // Bottom face

        // Top face triangles
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 2);
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 3);

        // Side faces (no bottom face to keep it simple)
        // Front face
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 4); triangles.Add(baseIndex + 1);
        triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 4); triangles.Add(baseIndex + 5);

        // Right face
        triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 5); triangles.Add(baseIndex + 2);
        triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 5); triangles.Add(baseIndex + 6);

        // Back face
        triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 6); triangles.Add(baseIndex + 3);
        triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 6); triangles.Add(baseIndex + 7);

        // Left face
        triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 7); triangles.Add(baseIndex + 0);
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 7); triangles.Add(baseIndex + 4);
    }











    public void ClearIsland()
    {
        if (meshFilter == null) meshFilter = GetComponent<MeshFilter>();
        if (meshCollider == null) meshCollider = GetComponent<MeshCollider>();

        if (meshFilter.sharedMesh != null)
        {
            DestroyImmediate(meshFilter.sharedMesh);
        }
        meshFilter.mesh = null;
        if (meshCollider.sharedMesh != null)
        {
            DestroyImmediate(meshCollider.sharedMesh);
        }
        meshCollider.sharedMesh = null;
    }

    // Method for inspector button and context menu
    [ContextMenu("Generate Island")]
    public void RegenerateIsland()
    {
        GenerateIsland();
    }
}

[CustomEditor(typeof(IslandGenerator))]
public class IslandGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        IslandGenerator generator = (IslandGenerator)target;

        if (GUILayout.Button("Generate Island"))
        {
            generator.GenerateIsland();
        }

        if (GUILayout.Button("Clear Island"))
        {
            generator.ClearIsland();
        }
    }
}