using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer), typeof(MeshCollider))]
public class IslandGenerator : MonoBehaviour
{
    [Header("Grid Settings")]
    public int size = 50;
    public float surfaceHeight = 0f;
    public float edgeDepth = 5f;

    [Header("Noise Settings")]
    public float noiseScale = 10f;
    public float islandThreshold = 0.4f;
    public int seed = 0;

    [Header("Shape Settings")]
    [Range(16,256)] public int segments = 128;
    public float coreRadius = 25f;
    public float coastVariation = 5f;

    [Header("Checkerboard Settings")]
    public float tileSize = 1f;

    [Header("Appearance")]
    public Color grassColorLight = new Color(0.45f, 0.76f, 0.34f);
    public Color grassColorDark = new Color(0.41f, 0.69f, 0.31f);
    public Color cliffColor = new Color(0.36f, 0.25f, 0.2f);

    [Header("Generation Settings")]
    public bool generateOnStart = true;
    public bool autoUpdateInEditor = true;

    private MeshFilter meshFilter;
    private MeshCollider meshCollider;
    private MeshRenderer meshRenderer;

    void Start()
    {
        if (generateOnStart)
        {
            GenerateIsland();
        }
    }

    void OnValidate()
    {
        if (autoUpdateInEditor && Application.isPlaying)
        {
            GenerateIsland();
        }
    }

    public void GenerateIsland()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshCollider = GetComponent<MeshCollider>();
        meshRenderer = GetComponent<MeshRenderer>();

        Mesh mesh = new Mesh();
        mesh.name = "ProceduralIsland";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Color> colors = new List<Color>();
        List<Vector3> normals = new List<Vector3>();

        // Generate island as discrete tile pieces that form organic shape
        GenerateOrganicTileIsland(vertices, triangles, colors, normals);

        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.colors = colors.ToArray();
        mesh.normals = normals.ToArray();
        mesh.RecalculateBounds();

        meshFilter.sharedMesh = mesh;
        if (meshCollider != null) meshCollider.sharedMesh = mesh;

        // material if not already
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Particles/Unlit"));
        mat.SetColor("_BaseColor", Color.white);
        if (mat.HasProperty("_Cull")) mat.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        if (mat.HasProperty("_CullMode")) mat.SetInt("_CullMode", (int)UnityEngine.Rendering.CullMode.Off);
        meshRenderer.sharedMaterial = mat;
    }

    void GenerateOrganicTileIsland(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals)
    {
        // Create a grid of potential tile positions
        int gridSize = Mathf.CeilToInt(coreRadius * 2f / tileSize) + 4; // Extra padding
        float startX = -gridSize * tileSize * 0.5f;
        float startZ = -gridSize * tileSize * 0.5f;

        // Create a 2D grid to track which tiles should exist
        bool[,] tileGrid = new bool[gridSize, gridSize];

        // Step 1: Create initial solid core
        CreateSolidCore(tileGrid, gridSize);

        // Step 2: Add organic variation to edges
        AddOrganicEdges(tileGrid, gridSize);

        // Step 3: Fill holes and remove isolated pieces
        CleanupIsland(tileGrid, gridSize);

        // Step 4: Generate actual 3D tiles from the cleaned grid
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                if (tileGrid[x, z])
                {
                    float tileX = startX + x * tileSize;
                    float tileZ = startZ + z * tileSize;

                    // Checkerboard pattern
                    bool isLight = ((x + z) & 1) == 0;
                    Color tileColor = isLight ? grassColorLight : grassColorDark;

                    // Create the 3D tile piece
                    CreateTilePiece(vertices, triangles, colors, normals, tileX, tileZ, tileSize, surfaceHeight, tileColor);
                }
            }
        }
    }

    void CreateSolidCore(bool[,] tileGrid, int gridSize)
    {
        int centerX = gridSize / 2;
        int centerZ = gridSize / 2;
        int coreRadiusInTiles = Mathf.FloorToInt(coreRadius / tileSize);

        // Create a solid circular core
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                int dx = x - centerX;
                int dz = z - centerZ;
                float distance = Mathf.Sqrt(dx * dx + dz * dz);

                if (distance <= coreRadiusInTiles * 0.7f) // Solid inner core
                {
                    tileGrid[x, z] = true;
                }
            }
        }
    }

    void AddOrganicEdges(bool[,] tileGrid, int gridSize)
    {
        System.Random rand = new System.Random(seed);
        int centerX = gridSize / 2;
        int centerZ = gridSize / 2;
        int coreRadiusInTiles = Mathf.FloorToInt(coreRadius / tileSize);

        // Add organic variation around the core
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                if (!tileGrid[x, z]) // Only consider empty tiles
                {
                    int dx = x - centerX;
                    int dz = z - centerZ;
                    float distance = Mathf.Sqrt(dx * dx + dz * dz);

                    // Only add tiles in the edge zone
                    if (distance > coreRadiusInTiles * 0.7f && distance <= coreRadiusInTiles * 1.2f)
                    {
                        // Use noise to determine if this edge tile should exist
                        float noiseValue = Mathf.PerlinNoise(x * noiseScale, z * noiseScale);
                        float threshold = 0.3f + (distance - coreRadiusInTiles * 0.7f) / (coreRadiusInTiles * 0.5f) * 0.4f;

                        if (noiseValue > threshold)
                        {
                            tileGrid[x, z] = true;
                        }
                    }
                }
            }
        }
    }

    void CleanupIsland(bool[,] tileGrid, int gridSize)
    {
        // Fill holes (empty tiles surrounded by ground)
        FillHoles(tileGrid, gridSize);

        // Remove isolated pieces (ground tiles not connected to main island)
        RemoveIsolatedPieces(tileGrid, gridSize);
    }

    void FillHoles(bool[,] tileGrid, int gridSize)
    {
        bool changed = true;
        while (changed)
        {
            changed = false;
            for (int x = 1; x < gridSize - 1; x++)
            {
                for (int z = 1; z < gridSize - 1; z++)
                {
                    if (!tileGrid[x, z]) // Empty tile
                    {
                        // Count surrounding ground tiles
                        int groundNeighbors = 0;
                        for (int dx = -1; dx <= 1; dx++)
                        {
                            for (int dz = -1; dz <= 1; dz++)
                            {
                                if (dx == 0 && dz == 0) continue;
                                if (tileGrid[x + dx, z + dz]) groundNeighbors++;
                            }
                        }

                        // If surrounded by mostly ground, fill it
                        if (groundNeighbors >= 6)
                        {
                            tileGrid[x, z] = true;
                            changed = true;
                        }
                    }
                }
            }
        }
    }

    void RemoveIsolatedPieces(bool[,] tileGrid, int gridSize)
    {
        // Find the largest connected component (main island)
        bool[,] visited = new bool[gridSize, gridSize];
        int largestSize = 0;
        bool[,] largestComponent = new bool[gridSize, gridSize];

        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                if (tileGrid[x, z] && !visited[x, z])
                {
                    // Found a new component, flood fill to find its size
                    bool[,] currentComponent = new bool[gridSize, gridSize];
                    int componentSize = FloodFill(tileGrid, visited, currentComponent, x, z, gridSize);

                    if (componentSize > largestSize)
                    {
                        largestSize = componentSize;
                        largestComponent = currentComponent;
                    }
                }
            }
        }

        // Keep only the largest component
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                tileGrid[x, z] = largestComponent[x, z];
            }
        }
    }

    int FloodFill(bool[,] tileGrid, bool[,] visited, bool[,] component, int startX, int startZ, int gridSize)
    {
        if (startX < 0 || startX >= gridSize || startZ < 0 || startZ >= gridSize) return 0;
        if (visited[startX, startZ] || !tileGrid[startX, startZ]) return 0;

        visited[startX, startZ] = true;
        component[startX, startZ] = true;
        int size = 1;

        // Check 4-connected neighbors
        size += FloodFill(tileGrid, visited, component, startX + 1, startZ, gridSize);
        size += FloodFill(tileGrid, visited, component, startX - 1, startZ, gridSize);
        size += FloodFill(tileGrid, visited, component, startX, startZ + 1, gridSize);
        size += FloodFill(tileGrid, visited, component, startX, startZ - 1, gridSize);

        return size;
    }

    void CreateTilePiece(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals,
                        float x, float z, float size, float height, Color color)
    {
        int baseIndex = vertices.Count;

        // Create vertices for a 3D tile piece with beveled edges like in the reference
        float bevelSize = size * 0.1f; // Small bevel for 3D effect
        float topHeight = height;
        float bottomHeight = height - edgeDepth;

        // Top face (slightly inset for bevel)
        vertices.Add(new Vector3(x + bevelSize, topHeight, z + bevelSize));           // 0
        vertices.Add(new Vector3(x + size - bevelSize, topHeight, z + bevelSize));    // 1
        vertices.Add(new Vector3(x + size - bevelSize, topHeight, z + size - bevelSize)); // 2
        vertices.Add(new Vector3(x + bevelSize, topHeight, z + size - bevelSize));    // 3

        // Bottom face (full size)
        vertices.Add(new Vector3(x, bottomHeight, z));                               // 4
        vertices.Add(new Vector3(x + size, bottomHeight, z));                        // 5
        vertices.Add(new Vector3(x + size, bottomHeight, z + size));                 // 6
        vertices.Add(new Vector3(x, bottomHeight, z + size));                        // 7

        // Add colors for all vertices
        for (int i = 0; i < 8; i++)
        {
            colors.Add(i < 4 ? color : cliffColor); // Top face gets tile color, bottom gets cliff color
        }

        // Add normals
        for (int i = 0; i < 4; i++) normals.Add(Vector3.up);    // Top face
        for (int i = 0; i < 4; i++) normals.Add(Vector3.down);  // Bottom face

        // Create triangles for top face
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 2);
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 3);

        // Create triangles for side faces (connecting top bevel to bottom edge)
        // Front face
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 4); triangles.Add(baseIndex + 1);
        triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 4); triangles.Add(baseIndex + 5);

        // Right face
        triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 5); triangles.Add(baseIndex + 2);
        triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 5); triangles.Add(baseIndex + 6);

        // Back face
        triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 6); triangles.Add(baseIndex + 3);
        triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 6); triangles.Add(baseIndex + 7);

        // Left face
        triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 7); triangles.Add(baseIndex + 0);
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 7); triangles.Add(baseIndex + 4);
    }

    void GenerateCheckerboardTiles(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals, int[] topRingIndices)
    {
        // Calculate how many tiles fit within the core radius
        int tilesPerSide = Mathf.FloorToInt(coreRadius * 2f / tileSize);
        float totalWidth = tilesPerSide * tileSize;
        float startX = -totalWidth * 0.5f;
        float startZ = -totalWidth * 0.5f;

        List<int> borderVertices = new List<int>();

        // Create individual 3D tiles for the checkerboard pattern
        for (int x = 0; x < tilesPerSide; x++)
        {
            for (int z = 0; z < tilesPerSide; z++)
            {
                float tileX = startX + x * tileSize;
                float tileZ = startZ + z * tileSize;
                Vector3 tileCenter = new Vector3(tileX + tileSize * 0.5f, 0, tileZ + tileSize * 0.5f);

                // Only create tiles that are within the core radius
                if (tileCenter.magnitude <= coreRadius * 0.9f)
                {
                    // Determine tile color (checkerboard pattern)
                    bool isLight = ((x + z) & 1) == 0;
                    Color tileColor = isLight ? grassColorLight : grassColorDark;

                    // Store the starting vertex index for this tile
                    int tileStartIndex = vertices.Count;

                    // Create a 3D tile (raised square)
                    CreateTile(vertices, triangles, colors, normals, tileX, tileZ, tileSize, surfaceHeight, tileColor);

                    // Check if this is a border tile (near the edge of our tile area)
                    bool isBorderTile = (x == 0 || x == tilesPerSide - 1 || z == 0 || z == tilesPerSide - 1);

                    if (isBorderTile)
                    {
                        // Add the top face vertices of border tiles for edge connection
                        for (int i = 0; i < 4; i++)
                        {
                            borderVertices.Add(tileStartIndex + i);
                        }
                    }
                }
            }
        }

        // Connect border tiles to the organic edge
        ConnectTilesToEdge(vertices, triangles, borderVertices, topRingIndices);
    }

    void CreateTile(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals,
                   float x, float z, float size, float height, Color color)
    {
        int baseIndex = vertices.Count;

        // Create 8 vertices for a 3D tile (4 top, 4 bottom)
        // Top face vertices
        vertices.Add(new Vector3(x, height, z));                    // 0: bottom-left
        vertices.Add(new Vector3(x + size, height, z));             // 1: bottom-right
        vertices.Add(new Vector3(x + size, height, z + size));      // 2: top-right
        vertices.Add(new Vector3(x, height, z + size));             // 3: top-left

        // Bottom face vertices (slightly lower for 3D effect)
        float bottomHeight = height - 0.1f;
        vertices.Add(new Vector3(x, bottomHeight, z));              // 4: bottom-left
        vertices.Add(new Vector3(x + size, bottomHeight, z));       // 5: bottom-right
        vertices.Add(new Vector3(x + size, bottomHeight, z + size)); // 6: top-right
        vertices.Add(new Vector3(x, bottomHeight, z + size));       // 7: top-left

        // Add colors and normals for all vertices
        for (int i = 0; i < 8; i++)
        {
            colors.Add(color);
        }

        // Top face normals (pointing up)
        for (int i = 0; i < 4; i++)
        {
            normals.Add(Vector3.up);
        }

        // Bottom face normals (pointing down)
        for (int i = 0; i < 4; i++)
        {
            normals.Add(Vector3.down);
        }

        // Create triangles for top face (2 triangles)
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 2);
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 3);

        // Create triangles for side faces (8 triangles total, 2 per side)
        // Front face
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 4); triangles.Add(baseIndex + 1);
        triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 4); triangles.Add(baseIndex + 5);

        // Right face
        triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 5); triangles.Add(baseIndex + 2);
        triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 5); triangles.Add(baseIndex + 6);

        // Back face
        triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 6); triangles.Add(baseIndex + 3);
        triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 6); triangles.Add(baseIndex + 7);

        // Left face
        triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 7); triangles.Add(baseIndex + 0);
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 7); triangles.Add(baseIndex + 4);
    }

    void ConnectTilesToEdge(List<Vector3> vertices, List<int> triangles, List<int> borderVertices, int[] topRingIndices)
    {
        // Simple fan triangulation from border tile vertices to edge
        for (int i = 0; i < topRingIndices.Length; i++)
        {
            int currentEdge = topRingIndices[i];
            int nextEdge = topRingIndices[(i + 1) % topRingIndices.Length];

            // Find closest border vertex to this edge segment
            int closestBorder = -1;
            float closestDist = float.MaxValue;

            foreach (int borderVertex in borderVertices)
            {
                if (borderVertex < vertices.Count) // Safety check
                {
                    float dist = Vector3.Distance(vertices[borderVertex], vertices[currentEdge]);
                    if (dist < closestDist)
                    {
                        closestDist = dist;
                        closestBorder = borderVertex;
                    }
                }
            }

            if (closestBorder != -1)
            {
                triangles.Add(currentEdge);
                triangles.Add(nextEdge);
                triangles.Add(closestBorder);
            }
        }
    }









    public void ClearIsland()
    {
        if (meshFilter == null) meshFilter = GetComponent<MeshFilter>();
        if (meshCollider == null) meshCollider = GetComponent<MeshCollider>();

        if (meshFilter.sharedMesh != null)
        {
            DestroyImmediate(meshFilter.sharedMesh);
        }
        meshFilter.mesh = null;
        if (meshCollider.sharedMesh != null)
        {
            DestroyImmediate(meshCollider.sharedMesh);
        }
        meshCollider.sharedMesh = null;
    }
}

[CustomEditor(typeof(IslandGenerator))]
public class IslandGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        IslandGenerator generator = (IslandGenerator)target;

        if (GUILayout.Button("Generate Island"))
        {
            generator.GenerateIsland();
        }

        if (GUILayout.Button("Clear Island"))
        {
            generator.ClearIsland();
        }
    }
}