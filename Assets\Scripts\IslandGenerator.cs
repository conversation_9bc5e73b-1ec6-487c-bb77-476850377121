using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer), typeof(MeshCollider))]
public class IslandGenerator : MonoBehaviour
{
    [Header("Grid Settings")]
    public int size = 50;
    public float surfaceHeight = 0f;
    public float edgeDepth = 5f;

    [Header("Noise Settings")]
    public float noiseScale = 10f;
    public float islandThreshold = 0.4f;
    public int seed = 0;

    [Header("Shape Settings")]
    [Range(16,256)] public int segments = 128;
    public float coreRadius = 25f;
    public float coastVariation = 5f;

    [Header("Checkerboard Settings")]
    public float tileSize = 1f;

    [Header("Appearance")]
    public Color grassColorLight = new Color(0.45f, 0.76f, 0.34f);
    public Color grassColorDark = new Color(0.41f, 0.69f, 0.31f);
    public Color cliffColor = new Color(0.36f, 0.25f, 0.2f);

    [Header("Generation Settings")]
    public bool generateOnStart = true;
    public bool autoUpdateInEditor = true;

    private MeshFilter meshFilter;
    private MeshCollider meshCollider;
    private MeshRenderer meshRenderer;

    void Start()
    {
        if (generateOnStart)
        {
            GenerateIsland();
        }
    }

    void OnValidate()
    {
        if (autoUpdateInEditor && Application.isPlaying)
        {
            GenerateIsland();
        }
    }

    public void GenerateIsland()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshCollider = GetComponent<MeshCollider>();
        meshRenderer = GetComponent<MeshRenderer>();

        Mesh mesh = new Mesh();
        mesh.name = "ProceduralIsland";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Color> colors = new List<Color>();
        List<Vector3> normals = new List<Vector3>();

        // --- noise setup ---
        System.Random rand = new System.Random(seed);
        Vector2 noiseOffset = new Vector2(rand.Next(0, 10000), rand.Next(0, 10000));

        // Precompute ring vertices for the edge
        int[] topRingIndices = new int[segments];
        int[] bottomRingIndices = new int[segments];

        float twoPi = Mathf.PI * 2f;
        for (int i = 0; i < segments; i++)
        {
            float angle = twoPi * i / segments;
            Vector2 dir = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle));
            float noise = Mathf.PerlinNoise(dir.x * noiseScale + noiseOffset.x, dir.y * noiseScale + noiseOffset.y);
            float radius = coreRadius + noise * coastVariation;

            Vector3 topPos = new Vector3(dir.x * radius, surfaceHeight, dir.y * radius);
            Vector3 botPos = new Vector3(dir.x * radius, surfaceHeight - edgeDepth, dir.y * radius);

            // Determine checkerboard colour based on world-space integer grid
            int ringIndex = Mathf.FloorToInt(radius);
            bool light = (((ringIndex + i) & 1) == 0);
            Color c = light ? grassColorLight : grassColorDark;

            // top vertex
            int topIdx = vertices.Count;
            vertices.Add(topPos);
            colors.Add(c);
            normals.Add(Vector3.up);
            topRingIndices[i] = topIdx;

            // bottom vertex
            int botIdx = vertices.Count;
            vertices.Add(botPos);
            colors.Add(cliffColor);
            normals.Add(dir.normalized); // outward
            bottomRingIndices[i] = botIdx;
        }

        // Generate checkerboard grid in the center
        GenerateCheckerboardCenter(vertices, triangles, colors, normals, topRingIndices);

        // --- build cliff quads ---
        for (int i = 0; i < segments; i++)
        {
            int next = (i + 1) % segments;

            // Cliff quad between segment edges
            int v00 = topRingIndices[i];
            int v01 = topRingIndices[next];
            int v11 = bottomRingIndices[next];
            int v10 = bottomRingIndices[i];

            // First triangle
            triangles.Add(v00);
            triangles.Add(v01);
            triangles.Add(v11);
            // Second triangle
            triangles.Add(v00);
            triangles.Add(v11);
            triangles.Add(v10);

            // Bottom cliff normals already set; ensure bottom vertices normals point outward too
            normals[v10] = (vertices[v10] - Vector3.zero).normalized;
            normals[v11] = (vertices[v11] - Vector3.zero).normalized;
        }

        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.colors = colors.ToArray();
        mesh.normals = normals.ToArray();
        mesh.RecalculateBounds();

        meshFilter.sharedMesh = mesh;
        if (meshCollider != null) meshCollider.sharedMesh = mesh;

        // material if not already
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Particles/Unlit"));
        mat.SetColor("_BaseColor", Color.white);
        if (mat.HasProperty("_Cull")) mat.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        if (mat.HasProperty("_CullMode")) mat.SetInt("_CullMode", (int)UnityEngine.Rendering.CullMode.Off);
        meshRenderer.sharedMaterial = mat;
    }

    void GenerateCheckerboardCenter(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals, int[] topRingIndices)
    {
        // Calculate the maximum radius to determine grid bounds
        float maxRadius = 0f;
        for (int i = 0; i < topRingIndices.Length; i++)
        {
            Vector3 pos = vertices[topRingIndices[i]];
            float radius = Mathf.Sqrt(pos.x * pos.x + pos.z * pos.z);
            maxRadius = Mathf.Max(maxRadius, radius);
        }

        // Create a grid of vertices for the checkerboard pattern
        int gridSize = Mathf.CeilToInt(maxRadius * 2f / tileSize) + 1;
        Vector3[,] gridVertices = new Vector3[gridSize, gridSize];
        int[,] gridIndices = new int[gridSize, gridSize];
        bool[,] gridValid = new bool[gridSize, gridSize];

        // Generate grid vertices
        float halfGrid = (gridSize - 1) * tileSize * 0.5f;
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                Vector3 worldPos = new Vector3((x * tileSize) - halfGrid, surfaceHeight, (z * tileSize) - halfGrid);
                float distFromCenter = Mathf.Sqrt(worldPos.x * worldPos.x + worldPos.z * worldPos.z);

                // Only include vertices that are inside the island boundary
                if (distFromCenter < maxRadius * 0.8f) // Use 80% to ensure we stay well inside
                {
                    gridVertices[x, z] = worldPos;
                    gridIndices[x, z] = vertices.Count;
                    gridValid[x, z] = true;

                    // Checkerboard color
                    bool isLight = ((x + z) & 1) == 0;
                    Color tileColor = isLight ? grassColorLight : grassColorDark;

                    vertices.Add(worldPos);
                    colors.Add(tileColor);
                    normals.Add(Vector3.up);
                }
                else
                {
                    gridValid[x, z] = false;
                }
            }
        }

        // Generate quads for the checkerboard
        for (int x = 0; x < gridSize - 1; x++)
        {
            for (int z = 0; z < gridSize - 1; z++)
            {
                if (gridValid[x, z] && gridValid[x + 1, z] && gridValid[x, z + 1] && gridValid[x + 1, z + 1])
                {
                    int v00 = gridIndices[x, z];
                    int v10 = gridIndices[x + 1, z];
                    int v01 = gridIndices[x, z + 1];
                    int v11 = gridIndices[x + 1, z + 1];

                    // Create two triangles for the quad
                    triangles.Add(v00);
                    triangles.Add(v01);
                    triangles.Add(v10);

                    triangles.Add(v10);
                    triangles.Add(v01);
                    triangles.Add(v11);
                }
            }
        }

        // Connect the grid to the edge ring
        ConnectGridToEdge(vertices, triangles, topRingIndices, gridVertices, gridIndices, gridValid, gridSize);
    }

    void ConnectGridToEdge(List<Vector3> vertices, List<int> triangles,
                          int[] topRingIndices, Vector3[,] gridVertices, int[,] gridIndices, bool[,] gridValid, int gridSize)
    {
        // For each edge vertex, find the closest grid vertices and create triangles to connect them
        for (int i = 0; i < topRingIndices.Length; i++)
        {
            Vector3 edgePos = vertices[topRingIndices[i]];
            int nextEdge = (i + 1) % topRingIndices.Length;
            Vector3 nextEdgePos = vertices[topRingIndices[nextEdge]];

            // Find grid vertices that are close to this edge segment
            List<int> nearbyGridVertices = new List<int>();
            float halfGrid = (gridSize - 1) * tileSize * 0.5f;

            for (int x = 0; x < gridSize; x++)
            {
                for (int z = 0; z < gridSize; z++)
                {
                    if (gridValid[x, z])
                    {
                        Vector3 gridPos = gridVertices[x, z];
                        float distToEdge = Vector3.Distance(gridPos, edgePos);
                        float distToNextEdge = Vector3.Distance(gridPos, nextEdgePos);

                        // If this grid vertex is close to the edge segment
                        if (distToEdge < tileSize * 2f || distToNextEdge < tileSize * 2f)
                        {
                            nearbyGridVertices.Add(gridIndices[x, z]);
                        }
                    }
                }
            }

            // Create triangles connecting edge to nearby grid vertices
            if (nearbyGridVertices.Count > 0)
            {
                // Sort by distance to current edge vertex
                nearbyGridVertices.Sort((a, b) =>
                    Vector3.Distance(vertices[a], edgePos).CompareTo(Vector3.Distance(vertices[b], edgePos)));

                // Connect to the closest grid vertex
                if (nearbyGridVertices.Count >= 1)
                {
                    triangles.Add(topRingIndices[i]);
                    triangles.Add(topRingIndices[nextEdge]);
                    triangles.Add(nearbyGridVertices[0]);
                }
            }
        }
    }



    public void ClearIsland()
    {
        if (meshFilter == null) meshFilter = GetComponent<MeshFilter>();
        if (meshCollider == null) meshCollider = GetComponent<MeshCollider>();

        if (meshFilter.sharedMesh != null)
        {
            DestroyImmediate(meshFilter.sharedMesh);
        }
        meshFilter.mesh = null;
        if (meshCollider.sharedMesh != null)
        {
            DestroyImmediate(meshCollider.sharedMesh);
        }
        meshCollider.sharedMesh = null;
    }
}

[CustomEditor(typeof(IslandGenerator))]
public class IslandGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        IslandGenerator generator = (IslandGenerator)target;

        if (GUILayout.Button("Generate Island"))
        {
            generator.GenerateIsland();
        }

        if (GUILayout.Button("Clear Island"))
        {
            generator.ClearIsland();
        }
    }
}