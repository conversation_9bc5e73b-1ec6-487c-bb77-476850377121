using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer), typeof(MeshCollider))]
public class IslandGenerator : MonoBehaviour
{
    [Header("Grid Settings")]
    public int size = 50;
    public float surfaceHeight = 0f;
    public float edgeDepth = 5f;

    [Header("Noise Settings")]
    public float noiseScale = 10f;
    public float islandThreshold = 0.4f;
    public int seed = 0;

    [Header("Shape Settings")]
    [Range(16,256)] public int segments = 128;
    public float coreRadius = 25f;
    public float coastVariation = 5f;

    [Header("Checkerboard Settings")]
    public float tileSize = 1f;

    [Header("Appearance")]
    public Color grassColorLight = new Color(0.45f, 0.76f, 0.34f);
    public Color grassColorDark = new Color(0.41f, 0.69f, 0.31f);
    public Color cliffColor = new Color(0.36f, 0.25f, 0.2f);

    [Header("Generation Settings")]
    public bool generateOnStart = true;
    public bool autoUpdateInEditor = true;

    private MeshFilter meshFilter;
    private MeshCollider meshCollider;
    private MeshRenderer meshRenderer;

    void Start()
    {
        if (generateOnStart)
        {
            GenerateIsland();
        }
    }

    void OnValidate()
    {
        if (autoUpdateInEditor && Application.isPlaying)
        {
            GenerateIsland();
        }
    }

    public void GenerateIsland()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshCollider = GetComponent<MeshCollider>();
        meshRenderer = GetComponent<MeshRenderer>();

        Mesh mesh = new Mesh();
        mesh.name = "ProceduralIsland";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Color> colors = new List<Color>();
        List<Vector3> normals = new List<Vector3>();

        // --- noise setup ---
        System.Random rand = new System.Random(seed);
        Vector2 noiseOffset = new Vector2(rand.Next(0, 10000), rand.Next(0, 10000));

        // Precompute ring vertices for the edge
        int[] topRingIndices = new int[segments];
        int[] bottomRingIndices = new int[segments];

        float twoPi = Mathf.PI * 2f;
        for (int i = 0; i < segments; i++)
        {
            float angle = twoPi * i / segments;
            Vector2 dir = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle));
            float noise = Mathf.PerlinNoise(dir.x * noiseScale + noiseOffset.x, dir.y * noiseScale + noiseOffset.y);
            float radius = coreRadius + noise * coastVariation;

            Vector3 topPos = new Vector3(dir.x * radius, surfaceHeight, dir.y * radius);
            Vector3 botPos = new Vector3(dir.x * radius, surfaceHeight - edgeDepth, dir.y * radius);

            // Determine checkerboard colour based on world-space integer grid
            int ringIndex = Mathf.FloorToInt(radius);
            bool light = (((ringIndex + i) & 1) == 0);
            Color c = light ? grassColorLight : grassColorDark;

            // top vertex
            int topIdx = vertices.Count;
            vertices.Add(topPos);
            colors.Add(c);
            normals.Add(Vector3.up);
            topRingIndices[i] = topIdx;

            // bottom vertex
            int botIdx = vertices.Count;
            vertices.Add(botPos);
            colors.Add(cliffColor);
            normals.Add(dir.normalized); // outward
            bottomRingIndices[i] = botIdx;
        }

        // Generate 3D checkerboard tiles like in the reference image
        GenerateCheckerboardTiles(vertices, triangles, colors, normals, topRingIndices);

        // --- build cliff quads ---
        for (int i = 0; i < segments; i++)
        {
            int next = (i + 1) % segments;

            // Cliff quad between segment edges
            int v00 = topRingIndices[i];
            int v01 = topRingIndices[next];
            int v11 = bottomRingIndices[next];
            int v10 = bottomRingIndices[i];

            // First triangle
            triangles.Add(v00);
            triangles.Add(v01);
            triangles.Add(v11);
            // Second triangle
            triangles.Add(v00);
            triangles.Add(v11);
            triangles.Add(v10);

            // Bottom cliff normals already set; ensure bottom vertices normals point outward too
            normals[v10] = (vertices[v10] - Vector3.zero).normalized;
            normals[v11] = (vertices[v11] - Vector3.zero).normalized;
        }

        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.colors = colors.ToArray();
        mesh.normals = normals.ToArray();
        mesh.RecalculateBounds();

        meshFilter.sharedMesh = mesh;
        if (meshCollider != null) meshCollider.sharedMesh = mesh;

        // material if not already
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Particles/Unlit"));
        mat.SetColor("_BaseColor", Color.white);
        if (mat.HasProperty("_Cull")) mat.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        if (mat.HasProperty("_CullMode")) mat.SetInt("_CullMode", (int)UnityEngine.Rendering.CullMode.Off);
        meshRenderer.sharedMaterial = mat;
    }

    void GenerateCheckerboardTiles(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals, int[] topRingIndices)
    {
        // Calculate how many tiles fit within the core radius
        int tilesPerSide = Mathf.FloorToInt(coreRadius * 2f / tileSize);
        float totalWidth = tilesPerSide * tileSize;
        float startX = -totalWidth * 0.5f;
        float startZ = -totalWidth * 0.5f;

        // Create individual 3D tiles for the checkerboard pattern
        for (int x = 0; x < tilesPerSide; x++)
        {
            for (int z = 0; z < tilesPerSide; z++)
            {
                float tileX = startX + x * tileSize;
                float tileZ = startZ + z * tileSize;
                Vector3 tileCenter = new Vector3(tileX + tileSize * 0.5f, 0, tileZ + tileSize * 0.5f);

                // Only create tiles that are within the core radius
                if (tileCenter.magnitude <= coreRadius * 0.9f)
                {
                    // Determine tile color (checkerboard pattern)
                    bool isLight = ((x + z) & 1) == 0;
                    Color tileColor = isLight ? grassColorLight : grassColorDark;

                    // Create a 3D tile (raised square)
                    CreateTile(vertices, triangles, colors, normals, tileX, tileZ, tileSize, surfaceHeight, tileColor);
                }
            }
        }

        // Fill any gaps between tiles and edge with simple triangulation
        FillGapsToEdge(vertices, triangles, colors, normals, topRingIndices, startX, startZ, tilesPerSide, tileSize);
    }

    void CreateTile(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals,
                   float x, float z, float size, float height, Color color)
    {
        int baseIndex = vertices.Count;

        // Create 8 vertices for a 3D tile (4 top, 4 bottom)
        // Top face vertices
        vertices.Add(new Vector3(x, height, z));                    // 0: bottom-left
        vertices.Add(new Vector3(x + size, height, z));             // 1: bottom-right
        vertices.Add(new Vector3(x + size, height, z + size));      // 2: top-right
        vertices.Add(new Vector3(x, height, z + size));             // 3: top-left

        // Bottom face vertices (slightly lower for 3D effect)
        float bottomHeight = height - 0.1f;
        vertices.Add(new Vector3(x, bottomHeight, z));              // 4: bottom-left
        vertices.Add(new Vector3(x + size, bottomHeight, z));       // 5: bottom-right
        vertices.Add(new Vector3(x + size, bottomHeight, z + size)); // 6: top-right
        vertices.Add(new Vector3(x, bottomHeight, z + size));       // 7: top-left

        // Add colors and normals for all vertices
        for (int i = 0; i < 8; i++)
        {
            colors.Add(color);
        }

        // Top face normals (pointing up)
        for (int i = 0; i < 4; i++)
        {
            normals.Add(Vector3.up);
        }

        // Bottom face normals (pointing down)
        for (int i = 0; i < 4; i++)
        {
            normals.Add(Vector3.down);
        }

        // Create triangles for top face (2 triangles)
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 2);
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 3);

        // Create triangles for side faces (8 triangles total, 2 per side)
        // Front face
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 4); triangles.Add(baseIndex + 1);
        triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 4); triangles.Add(baseIndex + 5);

        // Right face
        triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 5); triangles.Add(baseIndex + 2);
        triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 5); triangles.Add(baseIndex + 6);

        // Back face
        triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 6); triangles.Add(baseIndex + 3);
        triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 6); triangles.Add(baseIndex + 7);

        // Left face
        triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 7); triangles.Add(baseIndex + 0);
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 7); triangles.Add(baseIndex + 4);
    }

    void FillGapsToEdge(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals,
                       int[] topRingIndices, float startX, float startZ, int tilesPerSide, float tileSize)
    {
        // Create a simple transition from the tile area to the organic edge
        // Find the outermost tile vertices and connect them to the edge

        List<int> borderVertices = new List<int>();

        // Collect vertices from the outer edges of tiles that are within the core radius
        for (int x = 0; x < tilesPerSide; x++)
        {
            for (int z = 0; z < tilesPerSide; z++)
            {
                float tileX = startX + x * tileSize;
                float tileZ = startZ + z * tileSize;
                Vector3 tileCenter = new Vector3(tileX + tileSize * 0.5f, 0, tileZ + tileSize * 0.5f);

                if (tileCenter.magnitude <= coreRadius * 0.9f)
                {
                    // Check if this tile is on the border (has at least one neighbor missing)
                    bool isBorderTile = false;

                    // Check all 4 neighbors
                    for (int dx = -1; dx <= 1; dx += 2)
                    {
                        for (int dz = -1; dz <= 1; dz += 2)
                        {
                            if (dx == 0 && dz == 0) continue;

                            int neighborX = x + dx;
                            int neighborZ = z + dz;

                            if (neighborX < 0 || neighborX >= tilesPerSide ||
                                neighborZ < 0 || neighborZ >= tilesPerSide)
                            {
                                isBorderTile = true;
                                break;
                            }

                            float neighborTileX = startX + neighborX * tileSize;
                            float neighborTileZ = startZ + neighborZ * tileSize;
                            Vector3 neighborCenter = new Vector3(neighborTileX + tileSize * 0.5f, 0, neighborTileZ + tileSize * 0.5f);

                            if (neighborCenter.magnitude > coreRadius * 0.9f)
                            {
                                isBorderTile = true;
                                break;
                            }
                        }
                        if (isBorderTile) break;
                    }

                    if (isBorderTile)
                    {
                        // Add the top vertices of this border tile
                        int tileBaseIndex = vertices.Count - (tilesPerSide * tilesPerSide - (x * tilesPerSide + z)) * 8;
                        for (int i = 0; i < 4; i++)
                        {
                            borderVertices.Add(tileBaseIndex + i);
                        }
                    }
                }
            }
        }

        // Connect border vertices to edge using simple fan triangulation
        for (int i = 0; i < topRingIndices.Length; i++)
        {
            int currentEdge = topRingIndices[i];
            int nextEdge = topRingIndices[(i + 1) % topRingIndices.Length];

            // Find closest border vertex
            int closestBorder = -1;
            float closestDist = float.MaxValue;

            foreach (int borderVertex in borderVertices)
            {
                float dist = Vector3.Distance(vertices[borderVertex], vertices[currentEdge]);
                if (dist < closestDist)
                {
                    closestDist = dist;
                    closestBorder = borderVertex;
                }
            }

            if (closestBorder != -1)
            {
                triangles.Add(currentEdge);
                triangles.Add(nextEdge);
                triangles.Add(closestBorder);
            }
        }
    }









    public void ClearIsland()
    {
        if (meshFilter == null) meshFilter = GetComponent<MeshFilter>();
        if (meshCollider == null) meshCollider = GetComponent<MeshCollider>();

        if (meshFilter.sharedMesh != null)
        {
            DestroyImmediate(meshFilter.sharedMesh);
        }
        meshFilter.mesh = null;
        if (meshCollider.sharedMesh != null)
        {
            DestroyImmediate(meshCollider.sharedMesh);
        }
        meshCollider.sharedMesh = null;
    }
}

[CustomEditor(typeof(IslandGenerator))]
public class IslandGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        IslandGenerator generator = (IslandGenerator)target;

        if (GUILayout.Button("Generate Island"))
        {
            generator.GenerateIsland();
        }

        if (GUILayout.Button("Clear Island"))
        {
            generator.ClearIsland();
        }
    }
}