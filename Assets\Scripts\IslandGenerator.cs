using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer), typeof(MeshCollider))]
public class IslandGenerator : MonoBehaviour
{
    [Header("Grid Settings")]
    public int size = 50;
    public float surfaceHeight = 0f;
    public float edgeDepth = 5f;

    [Header("Noise Settings")]
    public float noiseScale = 10f;
    public float islandThreshold = 0.4f;
    public int seed = 0;

    [Header("Shape Settings")]
    [Range(16,256)] public int segments = 128;
    public float coreRadius = 25f;
    public float coastVariation = 5f;

    [Header("Checkerboard Settings")]
    public float tileSize = 1f;

    [Header("Appearance")]
    public Color grassColorLight = new Color(0.45f, 0.76f, 0.34f);
    public Color grassColorDark = new Color(0.41f, 0.69f, 0.31f);
    public Color cliffColor = new Color(0.36f, 0.25f, 0.2f);

    [Header("Generation Settings")]
    public bool generateOnStart = true;
    public bool autoUpdateInEditor = true;

    private MeshFilter meshFilter;
    private MeshCollider meshCollider;
    private MeshRenderer meshRenderer;

    void Start()
    {
        if (generateOnStart)
        {
            GenerateIsland();
        }
    }

    void OnValidate()
    {
        if (autoUpdateInEditor && Application.isPlaying)
        {
            GenerateIsland();
        }
    }

    public void GenerateIsland()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshCollider = GetComponent<MeshCollider>();
        meshRenderer = GetComponent<MeshRenderer>();

        Mesh mesh = new Mesh();
        mesh.name = "ProceduralIsland";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Color> colors = new List<Color>();
        List<Vector3> normals = new List<Vector3>();

        // --- noise setup ---
        System.Random rand = new System.Random(seed);
        Vector2 noiseOffset = new Vector2(rand.Next(0, 10000), rand.Next(0, 10000));

        // Precompute ring vertices for the edge
        int[] topRingIndices = new int[segments];
        int[] bottomRingIndices = new int[segments];

        float twoPi = Mathf.PI * 2f;
        for (int i = 0; i < segments; i++)
        {
            float angle = twoPi * i / segments;
            Vector2 dir = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle));
            float noise = Mathf.PerlinNoise(dir.x * noiseScale + noiseOffset.x, dir.y * noiseScale + noiseOffset.y);
            float radius = coreRadius + noise * coastVariation;

            Vector3 topPos = new Vector3(dir.x * radius, surfaceHeight, dir.y * radius);
            Vector3 botPos = new Vector3(dir.x * radius, surfaceHeight - edgeDepth, dir.y * radius);

            // Determine checkerboard colour based on world-space integer grid
            int ringIndex = Mathf.FloorToInt(radius);
            bool light = (((ringIndex + i) & 1) == 0);
            Color c = light ? grassColorLight : grassColorDark;

            // top vertex
            int topIdx = vertices.Count;
            vertices.Add(topPos);
            colors.Add(c);
            normals.Add(Vector3.up);
            topRingIndices[i] = topIdx;

            // bottom vertex
            int botIdx = vertices.Count;
            vertices.Add(botPos);
            colors.Add(cliffColor);
            normals.Add(dir.normalized); // outward
            bottomRingIndices[i] = botIdx;
        }

        // Generate checkerboard surface using dense grid with fan connection
        GenerateCheckerboardWithFan(vertices, triangles, colors, normals, topRingIndices);

        // --- build cliff quads ---
        for (int i = 0; i < segments; i++)
        {
            int next = (i + 1) % segments;

            // Cliff quad between segment edges
            int v00 = topRingIndices[i];
            int v01 = topRingIndices[next];
            int v11 = bottomRingIndices[next];
            int v10 = bottomRingIndices[i];

            // First triangle
            triangles.Add(v00);
            triangles.Add(v01);
            triangles.Add(v11);
            // Second triangle
            triangles.Add(v00);
            triangles.Add(v11);
            triangles.Add(v10);

            // Bottom cliff normals already set; ensure bottom vertices normals point outward too
            normals[v10] = (vertices[v10] - Vector3.zero).normalized;
            normals[v11] = (vertices[v11] - Vector3.zero).normalized;
        }

        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.colors = colors.ToArray();
        mesh.normals = normals.ToArray();
        mesh.RecalculateBounds();

        meshFilter.sharedMesh = mesh;
        if (meshCollider != null) meshCollider.sharedMesh = mesh;

        // material if not already
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Particles/Unlit"));
        mat.SetColor("_BaseColor", Color.white);
        if (mat.HasProperty("_Cull")) mat.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        if (mat.HasProperty("_CullMode")) mat.SetInt("_CullMode", (int)UnityEngine.Rendering.CullMode.Off);
        meshRenderer.sharedMaterial = mat;
    }

    void GenerateCheckerboardWithFan(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals, int[] topRingIndices)
    {
        // Calculate island bounds
        float minX = float.MaxValue, maxX = float.MinValue;
        float minZ = float.MaxValue, maxZ = float.MinValue;

        for (int i = 0; i < topRingIndices.Length; i++)
        {
            Vector3 pos = vertices[topRingIndices[i]];
            minX = Mathf.Min(minX, pos.x);
            maxX = Mathf.Max(maxX, pos.x);
            minZ = Mathf.Min(minZ, pos.z);
            maxZ = Mathf.Max(maxZ, pos.z);
        }

        // Create a dense grid covering 95% of the island
        float coveragePercent = 0.95f;
        float gridWidth = (maxX - minX) * coveragePercent;
        float gridHeight = (maxZ - minZ) * coveragePercent;
        float centerX = (minX + maxX) * 0.5f;
        float centerZ = (minZ + maxZ) * 0.5f;

        int gridSizeX = Mathf.CeilToInt(gridWidth / tileSize) + 1;
        int gridSizeZ = Mathf.CeilToInt(gridHeight / tileSize) + 1;

        List<int> gridVertices = new List<int>();

        // Generate grid vertices
        for (int x = 0; x < gridSizeX; x++)
        {
            for (int z = 0; z < gridSizeZ; z++)
            {
                float worldX = centerX - gridWidth * 0.5f + (x * tileSize);
                float worldZ = centerZ - gridHeight * 0.5f + (z * tileSize);
                Vector3 worldPos = new Vector3(worldX, surfaceHeight, worldZ);

                // Checkerboard color
                bool isLight = ((x + z) & 1) == 0;
                Color tileColor = isLight ? grassColorLight : grassColorDark;

                int vertexIndex = vertices.Count;
                vertices.Add(worldPos);
                colors.Add(tileColor);
                normals.Add(Vector3.up);
                gridVertices.Add(vertexIndex);
            }
        }

        // Create triangles for the grid (quads split into triangles)
        for (int x = 0; x < gridSizeX - 1; x++)
        {
            for (int z = 0; z < gridSizeZ - 1; z++)
            {
                int v00 = gridVertices[x * gridSizeZ + z];
                int v10 = gridVertices[(x + 1) * gridSizeZ + z];
                int v01 = gridVertices[x * gridSizeZ + (z + 1)];
                int v11 = gridVertices[(x + 1) * gridSizeZ + (z + 1)];

                // Create two triangles for the quad
                triangles.Add(v00);
                triangles.Add(v01);
                triangles.Add(v10);

                triangles.Add(v10);
                triangles.Add(v01);
                triangles.Add(v11);
            }
        }

        // Connect grid border to edge using fan triangulation
        ConnectGridBorderToEdge(vertices, triangles, gridVertices, gridSizeX, gridSizeZ, topRingIndices);
    }

    void ConnectGridBorderToEdge(List<Vector3> vertices, List<int> triangles, List<int> gridVertices, int gridSizeX, int gridSizeZ, int[] topRingIndices)
    {
        // For each edge vertex, create triangles to the nearest grid border vertices
        for (int i = 0; i < topRingIndices.Length; i++)
        {
            int currentEdge = topRingIndices[i];
            int nextEdge = topRingIndices[(i + 1) % topRingIndices.Length];

            Vector3 edgePos = vertices[currentEdge];
            Vector3 nextEdgePos = vertices[nextEdge];

            // Find the closest grid border vertices to this edge segment
            List<int> nearbyGridVertices = new List<int>();

            // Check border vertices of the grid
            for (int x = 0; x < gridSizeX; x++)
            {
                for (int z = 0; z < gridSizeZ; z++)
                {
                    // Only consider border vertices
                    if (x == 0 || x == gridSizeX - 1 || z == 0 || z == gridSizeZ - 1)
                    {
                        int gridIndex = gridVertices[x * gridSizeZ + z];
                        Vector3 gridPos = vertices[gridIndex];

                        // Check if this grid vertex is reasonably close to the edge segment
                        float distToEdge = Vector3.Distance(gridPos, edgePos);
                        float distToNextEdge = Vector3.Distance(gridPos, nextEdgePos);

                        if (distToEdge < coreRadius * 0.8f || distToNextEdge < coreRadius * 0.8f)
                        {
                            nearbyGridVertices.Add(gridIndex);
                        }
                    }
                }
            }

            // Connect to the closest grid vertex
            if (nearbyGridVertices.Count > 0)
            {
                // Sort by distance to current edge vertex
                nearbyGridVertices.Sort((a, b) =>
                    Vector3.Distance(vertices[a], edgePos).CompareTo(Vector3.Distance(vertices[b], edgePos)));

                // Create triangle connecting edge segment to closest grid vertex
                triangles.Add(currentEdge);
                triangles.Add(nextEdge);
                triangles.Add(nearbyGridVertices[0]);
            }
        }
    }

    bool IsPointInsideIsland(Vector3 point, List<Vector3> vertices, int[] topRingIndices)
    {
        // Use ray casting algorithm to determine if point is inside the polygon
        int intersections = 0;
        Vector2 testPoint = new Vector2(point.x, point.z);

        for (int i = 0; i < topRingIndices.Length; i++)
        {
            int next = (i + 1) % topRingIndices.Length;
            Vector3 v1 = vertices[topRingIndices[i]];
            Vector3 v2 = vertices[topRingIndices[next]];

            Vector2 p1 = new Vector2(v1.x, v1.z);
            Vector2 p2 = new Vector2(v2.x, v2.z);

            // Check if ray from test point to the right intersects with edge
            if (((p1.y > testPoint.y) != (p2.y > testPoint.y)) &&
                (testPoint.x < (p2.x - p1.x) * (testPoint.y - p1.y) / (p2.y - p1.y) + p1.x))
            {
                intersections++;
            }
        }

        // Point is inside if odd number of intersections
        return (intersections & 1) == 1;
    }





    public void ClearIsland()
    {
        if (meshFilter == null) meshFilter = GetComponent<MeshFilter>();
        if (meshCollider == null) meshCollider = GetComponent<MeshCollider>();

        if (meshFilter.sharedMesh != null)
        {
            DestroyImmediate(meshFilter.sharedMesh);
        }
        meshFilter.mesh = null;
        if (meshCollider.sharedMesh != null)
        {
            DestroyImmediate(meshCollider.sharedMesh);
        }
        meshCollider.sharedMesh = null;
    }
}

[CustomEditor(typeof(IslandGenerator))]
public class IslandGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        IslandGenerator generator = (IslandGenerator)target;

        if (GUILayout.Button("Generate Island"))
        {
            generator.GenerateIsland();
        }

        if (GUILayout.Button("Clear Island"))
        {
            generator.ClearIsland();
        }
    }
}