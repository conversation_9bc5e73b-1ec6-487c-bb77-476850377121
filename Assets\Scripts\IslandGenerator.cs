using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer), typeof(MeshCollider))]
public class IslandGenerator : MonoBehaviour
{
    [Header("Grid Settings")]
    public int size = 50;
    public float surfaceHeight = 0f;
    public float edgeDepth = 5f;

    [Header("Noise Settings")]
    public float noiseScale = 10f;
    public float islandThreshold = 0.4f;
    public int seed = 0;

    [Header("Shape Settings")]
    [Range(16,256)] public int segments = 128;
    public float coreRadius = 25f;
    public float coastVariation = 5f;

    [Header("Checkerboard Settings")]
    public float tileSize = 1f;

    [Header("Appearance")]
    public Color grassColorLight = new Color(0.45f, 0.76f, 0.34f);
    public Color grassColorDark = new Color(0.41f, 0.69f, 0.31f);
    public Color cliffColor = new Color(0.36f, 0.25f, 0.2f);

    [Header("Generation Settings")]
    public bool generateOnStart = true;
    public bool autoUpdateInEditor = true;

    private MeshFilter meshFilter;
    private MeshCollider meshCollider;
    private MeshRenderer meshRenderer;

    void Start()
    {
        if (generateOnStart)
        {
            GenerateIsland();
        }
    }

    void OnValidate()
    {
        if (autoUpdateInEditor && Application.isPlaying)
        {
            GenerateIsland();
        }
    }

    public void GenerateIsland()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshCollider = GetComponent<MeshCollider>();
        meshRenderer = GetComponent<MeshRenderer>();

        Mesh mesh = new Mesh();
        mesh.name = "ProceduralIsland";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Color> colors = new List<Color>();
        List<Vector3> normals = new List<Vector3>();

        // --- noise setup ---
        System.Random rand = new System.Random(seed);
        Vector2 noiseOffset = new Vector2(rand.Next(0, 10000), rand.Next(0, 10000));

        // Precompute ring vertices for the edge
        int[] topRingIndices = new int[segments];
        int[] bottomRingIndices = new int[segments];

        float twoPi = Mathf.PI * 2f;
        for (int i = 0; i < segments; i++)
        {
            float angle = twoPi * i / segments;
            Vector2 dir = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle));
            float noise = Mathf.PerlinNoise(dir.x * noiseScale + noiseOffset.x, dir.y * noiseScale + noiseOffset.y);
            float radius = coreRadius + noise * coastVariation;

            Vector3 topPos = new Vector3(dir.x * radius, surfaceHeight, dir.y * radius);
            Vector3 botPos = new Vector3(dir.x * radius, surfaceHeight - edgeDepth, dir.y * radius);

            // Determine checkerboard colour based on world-space integer grid
            int ringIndex = Mathf.FloorToInt(radius);
            bool light = (((ringIndex + i) & 1) == 0);
            Color c = light ? grassColorLight : grassColorDark;

            // top vertex
            int topIdx = vertices.Count;
            vertices.Add(topPos);
            colors.Add(c);
            normals.Add(Vector3.up);
            topRingIndices[i] = topIdx;

            // bottom vertex
            int botIdx = vertices.Count;
            vertices.Add(botPos);
            colors.Add(cliffColor);
            normals.Add(dir.normalized); // outward
            bottomRingIndices[i] = botIdx;
        }

        // Generate checkerboard surface using modified fan approach
        GenerateCheckerboardSurface(vertices, triangles, colors, normals, topRingIndices);

        // --- build cliff quads ---
        for (int i = 0; i < segments; i++)
        {
            int next = (i + 1) % segments;

            // Cliff quad between segment edges
            int v00 = topRingIndices[i];
            int v01 = topRingIndices[next];
            int v11 = bottomRingIndices[next];
            int v10 = bottomRingIndices[i];

            // First triangle
            triangles.Add(v00);
            triangles.Add(v01);
            triangles.Add(v11);
            // Second triangle
            triangles.Add(v00);
            triangles.Add(v11);
            triangles.Add(v10);

            // Bottom cliff normals already set; ensure bottom vertices normals point outward too
            normals[v10] = (vertices[v10] - Vector3.zero).normalized;
            normals[v11] = (vertices[v11] - Vector3.zero).normalized;
        }

        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.colors = colors.ToArray();
        mesh.normals = normals.ToArray();
        mesh.RecalculateBounds();

        meshFilter.sharedMesh = mesh;
        if (meshCollider != null) meshCollider.sharedMesh = mesh;

        // material if not already
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Particles/Unlit"));
        mat.SetColor("_BaseColor", Color.white);
        if (mat.HasProperty("_Cull")) mat.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        if (mat.HasProperty("_CullMode")) mat.SetInt("_CullMode", (int)UnityEngine.Rendering.CullMode.Off);
        meshRenderer.sharedMaterial = mat;
    }

    void GenerateCheckerboardSurface(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals, int[] topRingIndices)
    {
        // Create concentric rings of vertices to form a checkerboard pattern
        int numRings = Mathf.CeilToInt(coreRadius / tileSize);
        List<List<int>> rings = new List<List<int>>();

        // Center vertex
        int centerIndex = vertices.Count;
        vertices.Add(new Vector3(0, surfaceHeight, 0));
        colors.Add(grassColorLight); // Center is always light
        normals.Add(Vector3.up);

        // Create rings of vertices
        for (int ring = 1; ring <= numRings; ring++)
        {
            List<int> ringVertices = new List<int>();
            float ringRadius = ring * tileSize;
            int verticesInRing = Mathf.Max(8, ring * 8); // More vertices in outer rings

            for (int i = 0; i < verticesInRing; i++)
            {
                float angle = (float)i / verticesInRing * Mathf.PI * 2f;
                Vector3 pos = new Vector3(
                    Mathf.Cos(angle) * ringRadius,
                    surfaceHeight,
                    Mathf.Sin(angle) * ringRadius
                );

                // Determine checkerboard color based on ring and position
                int gridX = Mathf.RoundToInt(pos.x / tileSize);
                int gridZ = Mathf.RoundToInt(pos.z / tileSize);
                bool isLight = ((gridX + gridZ) & 1) == 0;
                Color vertexColor = isLight ? grassColorLight : grassColorDark;

                int vertexIndex = vertices.Count;
                vertices.Add(pos);
                colors.Add(vertexColor);
                normals.Add(Vector3.up);
                ringVertices.Add(vertexIndex);
            }

            rings.Add(ringVertices);
        }

        // Connect center to first ring
        if (rings.Count > 0)
        {
            List<int> firstRing = rings[0];
            for (int i = 0; i < firstRing.Count; i++)
            {
                int next = (i + 1) % firstRing.Count;
                triangles.Add(centerIndex);
                triangles.Add(firstRing[next]);
                triangles.Add(firstRing[i]);
            }
        }

        // Connect rings to each other
        for (int r = 0; r < rings.Count - 1; r++)
        {
            ConnectRings(triangles, rings[r], rings[r + 1]);
        }

        // Connect outermost ring to edge
        if (rings.Count > 0)
        {
            ConnectRingToEdge(triangles, rings[rings.Count - 1], topRingIndices);
        }
    }

    void ConnectRings(List<int> triangles, List<int> innerRing, List<int> outerRing)
    {
        // Connect two rings with triangles
        int innerCount = innerRing.Count;
        int outerCount = outerRing.Count;

        float innerStep = (float)innerCount / outerCount;

        for (int i = 0; i < outerCount; i++)
        {
            int nextOuter = (i + 1) % outerCount;
            int innerIndex = Mathf.RoundToInt(i * innerStep) % innerCount;
            int nextInner = (innerIndex + 1) % innerCount;

            // Create triangles to connect the rings
            triangles.Add(innerRing[innerIndex]);
            triangles.Add(outerRing[i]);
            triangles.Add(outerRing[nextOuter]);

            if (i * innerStep - innerIndex > 0.5f)
            {
                triangles.Add(innerRing[innerIndex]);
                triangles.Add(outerRing[nextOuter]);
                triangles.Add(innerRing[nextInner]);
            }
        }
    }

    void ConnectRingToEdge(List<int> triangles, List<int> outerRing, int[] edgeRing)
    {
        // Connect the outermost ring to the edge vertices
        int ringCount = outerRing.Count;
        int edgeCount = edgeRing.Length;

        float step = (float)ringCount / edgeCount;

        for (int i = 0; i < edgeCount; i++)
        {
            int nextEdge = (i + 1) % edgeCount;
            int ringIndex = Mathf.RoundToInt(i * step) % ringCount;
            int nextRing = Mathf.RoundToInt((i + 1) * step) % ringCount;

            // Create triangles connecting edge to ring
            triangles.Add(edgeRing[i]);
            triangles.Add(edgeRing[nextEdge]);
            triangles.Add(outerRing[ringIndex]);

            if (nextRing != ringIndex)
            {
                triangles.Add(outerRing[ringIndex]);
                triangles.Add(edgeRing[nextEdge]);
                triangles.Add(outerRing[nextRing]);
            }
        }
    }

    bool IsPointInsideIsland(Vector3 point, List<Vector3> vertices, int[] topRingIndices)
    {
        // Use ray casting algorithm to determine if point is inside the polygon
        int intersections = 0;
        Vector2 testPoint = new Vector2(point.x, point.z);

        for (int i = 0; i < topRingIndices.Length; i++)
        {
            int next = (i + 1) % topRingIndices.Length;
            Vector3 v1 = vertices[topRingIndices[i]];
            Vector3 v2 = vertices[topRingIndices[next]];

            Vector2 p1 = new Vector2(v1.x, v1.z);
            Vector2 p2 = new Vector2(v2.x, v2.z);

            // Check if ray from test point to the right intersects with edge
            if (((p1.y > testPoint.y) != (p2.y > testPoint.y)) &&
                (testPoint.x < (p2.x - p1.x) * (testPoint.y - p1.y) / (p2.y - p1.y) + p1.x))
            {
                intersections++;
            }
        }

        // Point is inside if odd number of intersections
        return (intersections & 1) == 1;
    }

    void ConnectGridToEdge(List<Vector3> vertices, List<int> triangles,
                          int[] topRingIndices, Vector3[,] gridVertices, int[,] gridIndices, bool[,] gridValid, int gridSizeX, int gridSizeZ)
    {
        // Create a Delaunay-like triangulation to fill gaps between grid and edge
        List<int> allInteriorVertices = new List<int>();

        // Collect all valid grid vertices
        for (int x = 0; x < gridSizeX; x++)
        {
            for (int z = 0; z < gridSizeZ; z++)
            {
                if (gridValid[x, z])
                {
                    allInteriorVertices.Add(gridIndices[x, z]);
                }
            }
        }

        // For each edge segment, create triangles to fill the gap
        for (int i = 0; i < topRingIndices.Length; i++)
        {
            int currentEdge = topRingIndices[i];
            int nextEdge = topRingIndices[(i + 1) % topRingIndices.Length];

            Vector3 edgePos = vertices[currentEdge];
            Vector3 nextEdgePos = vertices[nextEdge];

            // Find the closest interior vertex to this edge segment
            int closestVertex = -1;
            float closestDist = float.MaxValue;

            foreach (int vertexIndex in allInteriorVertices)
            {
                Vector3 vertexPos = vertices[vertexIndex];

                // Calculate distance to the edge segment
                float distToSegment = DistanceToLineSegment(vertexPos, edgePos, nextEdgePos);

                if (distToSegment < closestDist)
                {
                    closestDist = distToSegment;
                    closestVertex = vertexIndex;
                }
            }

            // Create triangle connecting edge segment to closest interior vertex
            if (closestVertex != -1)
            {
                triangles.Add(currentEdge);
                triangles.Add(nextEdge);
                triangles.Add(closestVertex);
            }
        }
    }

    float DistanceToLineSegment(Vector3 point, Vector3 lineStart, Vector3 lineEnd)
    {
        Vector3 line = lineEnd - lineStart;
        float lineLength = line.magnitude;

        if (lineLength < 0.001f)
            return Vector3.Distance(point, lineStart);

        Vector3 lineDir = line / lineLength;
        Vector3 toPoint = point - lineStart;

        float projectionLength = Vector3.Dot(toPoint, lineDir);
        projectionLength = Mathf.Clamp(projectionLength, 0f, lineLength);

        Vector3 closestPointOnLine = lineStart + lineDir * projectionLength;
        return Vector3.Distance(point, closestPointOnLine);
    }



    public void ClearIsland()
    {
        if (meshFilter == null) meshFilter = GetComponent<MeshFilter>();
        if (meshCollider == null) meshCollider = GetComponent<MeshCollider>();

        if (meshFilter.sharedMesh != null)
        {
            DestroyImmediate(meshFilter.sharedMesh);
        }
        meshFilter.mesh = null;
        if (meshCollider.sharedMesh != null)
        {
            DestroyImmediate(meshCollider.sharedMesh);
        }
        meshCollider.sharedMesh = null;
    }
}

[CustomEditor(typeof(IslandGenerator))]
public class IslandGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        IslandGenerator generator = (IslandGenerator)target;

        if (GUILayout.Button("Generate Island"))
        {
            generator.GenerateIsland();
        }

        if (GUILayout.Button("Clear Island"))
        {
            generator.ClearIsland();
        }
    }
}