using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer), typeof(MeshCollider))]
public class IslandGenerator : MonoBehaviour
{
    [Header("Island Settings")]
    [Tooltip("Overall size of the island in world units")]
    public float size = 50f;
    [Tooltip("Height of the island surface")]
    public float surfaceHeight = 0f;
    [Tooltip("Depth of the cliff edges")]
    public float edgeDepth = 5f;

    [Header("Shape Settings")]
    [Range(0f, 1f)]
    [Tooltip("How jagged the coastline is (0 = smooth circle, 1 = very jagged)")]
    public float coastVariation = 0.5f;
    [Tooltip("Random seed for island shape generation")]
    public int seed = 0;

    [Header("Checkerboard Settings")]
    [Tooltip("Size of each checkerboard tile")]
    public float tileSize = 1f;

    [Header("Appearance")]
    public Color grassColorLight = new Color(0.45f, 0.76f, 0.34f);
    public Color grassColorDark = new Color(0.41f, 0.69f, 0.31f);
    public Color cliffColor = new Color(0.36f, 0.25f, 0.2f);

    [Header("Generation Settings")]
    public bool generateOnStart = true;
    public bool autoUpdateInEditor = true;

    private MeshFilter meshFilter;
    private MeshCollider meshCollider;
    private MeshRenderer meshRenderer;

    void Start()
    {
        if (generateOnStart)
        {
            GenerateIsland();
        }
    }

    void OnValidate()
    {
        if (autoUpdateInEditor && Application.isPlaying)
        {
            GenerateIsland();
        }
    }

    public void GenerateIsland()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshCollider = GetComponent<MeshCollider>();
        meshRenderer = GetComponent<MeshRenderer>();

        Mesh mesh = new Mesh();
        mesh.name = "ProceduralIsland";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Color> colors = new List<Color>();
        List<Vector3> normals = new List<Vector3>();

        // Generate island as discrete tile pieces that form organic shape
        GenerateOrganicTileIsland(vertices, triangles, colors, normals);

        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.colors = colors.ToArray();
        mesh.normals = normals.ToArray();
        mesh.RecalculateBounds();

        meshFilter.sharedMesh = mesh;
        if (meshCollider != null) meshCollider.sharedMesh = mesh;

        // material if not already
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Particles/Unlit"));
        mat.SetColor("_BaseColor", Color.white);
        if (mat.HasProperty("_Cull")) mat.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        if (mat.HasProperty("_CullMode")) mat.SetInt("_CullMode", (int)UnityEngine.Rendering.CullMode.Off);
        meshRenderer.sharedMaterial = mat;
    }

    void GenerateOrganicTileIsland(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals)
    {
        // Use the Size parameter to determine island size in tiles
        int islandSizeInTiles = Mathf.Max(10, Mathf.RoundToInt(size / tileSize));
        int gridSize = islandSizeInTiles + 8; // Extra padding for edge variation

        // Safety check to prevent huge grids that could cause performance issues
        if (gridSize > 200)
        {
            Debug.LogWarning($"Island grid size ({gridSize}) is very large. Consider increasing tile size or reducing island size.");
            gridSize = 200; // Cap at reasonable size
            islandSizeInTiles = gridSize - 8;
        }

        float startX = -gridSize * tileSize * 0.5f;
        float startZ = -gridSize * tileSize * 0.5f;

        // Create a 2D grid to track which tiles should exist
        bool[,] tileGrid = new bool[gridSize, gridSize];

        // Step 1: Create initial solid core
        CreateSolidCore(tileGrid, gridSize, islandSizeInTiles);

        // Step 2: Add organic variation to edges using coastVariation
        AddOrganicEdges(tileGrid, gridSize, islandSizeInTiles);

        // Step 3: Fill holes and remove isolated pieces
        CleanupIsland(tileGrid, gridSize);

        Debug.Log($"Generated island with grid size {gridSize}x{gridSize}, island size {islandSizeInTiles} tiles");

        // Step 4: Generate actual 3D tiles from the cleaned grid (no gaps!)
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                if (tileGrid[x, z])
                {
                    float tileX = startX + x * tileSize;
                    float tileZ = startZ + z * tileSize;

                    // Checkerboard pattern
                    bool isLight = ((x + z) & 1) == 0;
                    Color tileColor = isLight ? grassColorLight : grassColorDark;

                    // Create seamless tile piece (no bevels)
                    CreateSeamlessTile(vertices, triangles, colors, normals, tileX, tileZ, tileSize, surfaceHeight, tileColor);
                }
            }
        }
    }

    void CreateSolidCore(bool[,] tileGrid, int gridSize, int islandSizeInTiles)
    {
        int centerX = gridSize / 2;
        int centerZ = gridSize / 2;
        int coreRadius = islandSizeInTiles / 3; // Solid core is 1/3 of total island size

        // Create a solid circular core
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                int dx = x - centerX;
                int dz = z - centerZ;
                float distance = Mathf.Sqrt(dx * dx + dz * dz);

                if (distance <= coreRadius)
                {
                    tileGrid[x, z] = true;
                }
            }
        }
    }

    void AddOrganicEdges(bool[,] tileGrid, int gridSize, int islandSizeInTiles)
    {
        int centerX = gridSize / 2;
        int centerZ = gridSize / 2;
        int coreRadius = islandSizeInTiles / 3;
        int maxRadius = islandSizeInTiles / 2;

        // Add organic variation around the core using coastVariation
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                if (!tileGrid[x, z]) // Only consider empty tiles
                {
                    int dx = x - centerX;
                    int dz = z - centerZ;
                    float distance = Mathf.Sqrt(dx * dx + dz * dz);

                    // Only add tiles in the edge zone
                    if (distance > coreRadius && distance <= maxRadius)
                    {
                        // Use noise to determine if this edge tile should exist
                        float noiseValue = Mathf.PerlinNoise(x * 0.1f, z * 0.1f); // Fixed noise scale

                        // coastVariation controls how jagged the edges are (0 = smooth circle, 1 = very jagged)
                        float edgeProgress = (distance - coreRadius) / (maxRadius - coreRadius);
                        float baseThreshold = 0.3f + edgeProgress * 0.4f; // Gradually harder to place tiles toward edge
                        float variationAmount = coastVariation * 0.3f; // How much noise affects the threshold
                        float threshold = baseThreshold + (noiseValue - 0.5f) * variationAmount;

                        if (noiseValue > threshold)
                        {
                            tileGrid[x, z] = true;
                        }
                    }
                }
            }
        }
    }

    void CleanupIsland(bool[,] tileGrid, int gridSize)
    {
        // Fill holes (empty tiles surrounded by ground)
        FillHoles(tileGrid, gridSize);

        // Remove isolated pieces (ground tiles not connected to main island)
        RemoveIsolatedPieces(tileGrid, gridSize);
    }

    void FillHoles(bool[,] tileGrid, int gridSize)
    {
        bool changed = true;
        while (changed)
        {
            changed = false;
            for (int x = 1; x < gridSize - 1; x++)
            {
                for (int z = 1; z < gridSize - 1; z++)
                {
                    if (!tileGrid[x, z]) // Empty tile
                    {
                        // Count surrounding ground tiles
                        int groundNeighbors = 0;
                        for (int dx = -1; dx <= 1; dx++)
                        {
                            for (int dz = -1; dz <= 1; dz++)
                            {
                                if (dx == 0 && dz == 0) continue;
                                if (tileGrid[x + dx, z + dz]) groundNeighbors++;
                            }
                        }

                        // If surrounded by mostly ground, fill it
                        if (groundNeighbors >= 6)
                        {
                            tileGrid[x, z] = true;
                            changed = true;
                        }
                    }
                }
            }
        }
    }

    void RemoveIsolatedPieces(bool[,] tileGrid, int gridSize)
    {
        // Find the largest connected component (main island)
        bool[,] visited = new bool[gridSize, gridSize];
        int largestSize = 0;
        bool[,] largestComponent = new bool[gridSize, gridSize];

        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                if (tileGrid[x, z] && !visited[x, z])
                {
                    // Found a new component, flood fill to find its size
                    bool[,] currentComponent = new bool[gridSize, gridSize];
                    int componentSize = FloodFill(tileGrid, visited, currentComponent, x, z, gridSize);

                    if (componentSize > largestSize)
                    {
                        largestSize = componentSize;
                        largestComponent = currentComponent;
                    }
                }
            }
        }

        // Keep only the largest component
        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                tileGrid[x, z] = largestComponent[x, z];
            }
        }
    }

    int FloodFill(bool[,] tileGrid, bool[,] visited, bool[,] component, int startX, int startZ, int gridSize)
    {
        // Use iterative approach with a stack to avoid stack overflow
        Stack<Vector2Int> stack = new Stack<Vector2Int>();
        stack.Push(new Vector2Int(startX, startZ));
        int size = 0;

        while (stack.Count > 0)
        {
            Vector2Int current = stack.Pop();
            int x = current.x;
            int z = current.y;

            // Check bounds and validity
            if (x < 0 || x >= gridSize || z < 0 || z >= gridSize) continue;
            if (visited[x, z] || !tileGrid[x, z]) continue;

            // Mark as visited and part of component
            visited[x, z] = true;
            component[x, z] = true;
            size++;

            // Add 4-connected neighbors to stack
            stack.Push(new Vector2Int(x + 1, z));
            stack.Push(new Vector2Int(x - 1, z));
            stack.Push(new Vector2Int(x, z + 1));
            stack.Push(new Vector2Int(x, z - 1));
        }

        return size;
    }



    void CreateSeamlessTile(List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals,
                           float x, float z, float size, float height, Color color)
    {
        int baseIndex = vertices.Count;

        // Create perfectly aligned tiles with no gaps or bevels
        // Top face vertices
        vertices.Add(new Vector3(x, height, z));                    // 0
        vertices.Add(new Vector3(x + size, height, z));             // 1
        vertices.Add(new Vector3(x + size, height, z + size));      // 2
        vertices.Add(new Vector3(x, height, z + size));             // 3

        // Bottom face vertices
        vertices.Add(new Vector3(x, height - edgeDepth, z));                    // 4
        vertices.Add(new Vector3(x + size, height - edgeDepth, z));             // 5
        vertices.Add(new Vector3(x + size, height - edgeDepth, z + size));      // 6
        vertices.Add(new Vector3(x, height - edgeDepth, z + size));             // 7

        // Add colors for all vertices
        for (int i = 0; i < 8; i++)
        {
            colors.Add(i < 4 ? color : cliffColor); // Top face gets tile color, bottom gets cliff color
        }

        // Add normals
        for (int i = 0; i < 4; i++) normals.Add(Vector3.up);    // Top face
        for (int i = 0; i < 4; i++) normals.Add(Vector3.down);  // Bottom face

        // Top face triangles
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 2);
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 3);

        // Side faces (no bottom face to keep it simple)
        // Front face
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 4); triangles.Add(baseIndex + 1);
        triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 4); triangles.Add(baseIndex + 5);

        // Right face
        triangles.Add(baseIndex + 1); triangles.Add(baseIndex + 5); triangles.Add(baseIndex + 2);
        triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 5); triangles.Add(baseIndex + 6);

        // Back face
        triangles.Add(baseIndex + 2); triangles.Add(baseIndex + 6); triangles.Add(baseIndex + 3);
        triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 6); triangles.Add(baseIndex + 7);

        // Left face
        triangles.Add(baseIndex + 3); triangles.Add(baseIndex + 7); triangles.Add(baseIndex + 0);
        triangles.Add(baseIndex + 0); triangles.Add(baseIndex + 7); triangles.Add(baseIndex + 4);
    }











    public void ClearIsland()
    {
        if (meshFilter == null) meshFilter = GetComponent<MeshFilter>();
        if (meshCollider == null) meshCollider = GetComponent<MeshCollider>();

        if (meshFilter.sharedMesh != null)
        {
            DestroyImmediate(meshFilter.sharedMesh);
        }
        meshFilter.mesh = null;
        if (meshCollider.sharedMesh != null)
        {
            DestroyImmediate(meshCollider.sharedMesh);
        }
        meshCollider.sharedMesh = null;
    }

    // Method for inspector button and context menu
    [ContextMenu("Generate Island")]
    public void RegenerateIsland()
    {
        GenerateIsland();
    }
}

[CustomEditor(typeof(IslandGenerator))]
public class IslandGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        IslandGenerator generator = (IslandGenerator)target;

        if (GUILayout.Button("Generate Island"))
        {
            generator.GenerateIsland();
        }

        if (GUILayout.Button("Clear Island"))
        {
            generator.ClearIsland();
        }
    }
}